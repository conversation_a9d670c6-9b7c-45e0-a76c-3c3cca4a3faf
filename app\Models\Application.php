<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Application extends Model
{
    protected $fillable = [
        'user_id',
        'application_type_id',
        'title',
        'application_date',
        'description',
        'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function applicationType()
    {
        return $this->belongsTo(ApplicationType::class);
    }
}
