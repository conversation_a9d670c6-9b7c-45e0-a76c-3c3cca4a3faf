@extends('layouts.app')

@push('styles')
<style>
    .shift-form-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-header {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }

    .form-header h3 {
        margin: 0;
        font-weight: 600;
    }

    .form-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #E9A852;
        box-shadow: 0 0 0 0.2rem rgba(233, 168, 82, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(233, 168, 82, 0.3);
    }

    .recurring-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 1rem;
        border: 2px dashed #dee2e6;
    }

    .section-title {
        color: #E9A852;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
    }
</style>
@endpush

@section('content')
<div class="shift-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <h3><i class="fas fa-calendar-plus me-2"></i>Assign New Shift</h3>
                        <p class="mb-0">Create and assign shifts to staff members</p>
                    </div>

                    <div class="form-body">
                        <form action="{{ route('shifts.store') }}" method="POST" id="shiftForm">
                            @csrf

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="user_id">
                                            <i class="fas fa-user me-1"></i>Staff Member
                                        </label>
                                        <select name="user_id" id="user_id" class="form-control" required>
                                            <option value="">Select Staff Member</option>
                                            @foreach ($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="company_id">
                                            <i class="fas fa-building me-1"></i>Company
                                        </label>
                                        <select name="company_id" id="company_id" class="form-control" required>
                                            <option value="">Select Company</option>
                                            @foreach ($companies as $company)
                                                <option value="{{ $company->id }}">{{ $company->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="start_time">
                                            <i class="fas fa-clock me-1"></i>Start Time
                                        </label>
                                        <input type="datetime-local" name="start_time" id="start_time" class="form-control" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="end_time">
                                            <i class="fas fa-clock me-1"></i>End Time
                                        </label>
                                        <input type="datetime-local" name="end_time" id="end_time" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="shift_type">
                                            <i class="fas fa-tags me-1"></i>Shift Type
                                        </label>
                                        <select name="shift_type" id="shift_type" class="form-control" required>
                                            <option value="regular">Regular</option>
                                            <option value="overtime">Overtime</option>
                                            <option value="holiday">Holiday</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="hourly_rate">
                                            <i class="fas fa-pound-sign me-1"></i>Hourly Rate (Optional)
                                        </label>
                                        <input type="number" name="hourly_rate" id="hourly_rate" class="form-control" step="0.01" min="0" placeholder="Leave empty for default rate">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="notes">
                                    <i class="fas fa-sticky-note me-1"></i>Notes (Optional)
                                </label>
                                <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Add any additional notes or instructions..."></textarea>
                            </div>

                            <!-- Recurring Shifts Section -->
                            <div class="recurring-section">
                                <div class="section-title">
                                    <i class="fas fa-repeat"></i>Recurring Shifts (Optional)
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label" for="recurring_pattern">Repeat Pattern</label>
                                            <select name="recurring_pattern" id="recurring_pattern" class="form-control">
                                                <option value="">No Repeat</option>
                                                <option value="daily">Daily</option>
                                                <option value="weekly">Weekly</option>
                                                <option value="monthly">Monthly</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label" for="recurring_until">Repeat Until</label>
                                            <input type="date" name="recurring_until" id="recurring_until" class="form-control" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Assign Shift
                                </button>
                                <a href="{{ route('shifts.index') }}" class="btn btn-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recurringPattern = document.getElementById('recurring_pattern');
    const recurringUntil = document.getElementById('recurring_until');

    recurringPattern.addEventListener('change', function() {
        if (this.value) {
            recurringUntil.disabled = false;
            recurringUntil.required = true;
        } else {
            recurringUntil.disabled = true;
            recurringUntil.required = false;
            recurringUntil.value = '';
        }
    });
});
</script>
@endsection
