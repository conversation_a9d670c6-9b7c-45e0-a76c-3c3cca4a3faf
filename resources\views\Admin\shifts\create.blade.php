@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header">
            <h5>Assign New Shift</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('shifts.store') }}" method="POST">
                @csrf
                <div class="form-group">
                    <label for="user_id">Staff Member</label>
                    <select name="user_id" class="form-control" required>
                        @foreach ($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label for="company_id">Company</label>
                    <select name="company_id" class="form-control" required>
                        @foreach ($companies as $company)
                            <option value="{{ $company->id }}">{{ $company->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label for="start_time">Start Time</label>
                    <input type="datetime-local" name="start_time" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="end_time">End Time</label>
                    <input type="datetime-local" name="end_time" class="form-control" required>
                </div>
                <button class="btn btn-success mt-3">Assign Shift</button>
            </form>
        </div>
    </div>
@endsection
