<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Shift;
use App\Models\StaffLogin;
use Illuminate\Support\Facades\Auth;


class StaffController extends Controller
{
    public function profile()
    {
        return view('staff.profile');
    }

    public function clockin()
    {
        return view('staff.clockin');
    }

    // public function timesheet()
    // {
    //     $logins = StaffLogin::where('user_id', Auth::id())->latest()->get();
    //     return view('staff.timesheet', compact('logins'));
    // }

    public function alerts()
    {

        // return view('staff.alerts');
    }

    public function timetable()
    {
        $shifts = Shift::where('user_id', Auth::id())->upcoming()->get();
        return view('staff.timetable', compact('shifts'));
    }

    public function applications()
    {
        return view('staff.applications');
    }

    public function timesheet(Request $request)
    {
        $authUser = Auth::user();

        // Default to logged-in user unless admin is viewing someone else
        $user = $authUser;

        if ($authUser->hasRole('Admin') && $request->has('user_id')) {
            $user = \App\Models\User::findOrFail($request->user_id);
        }

        $logins = StaffLogin::with('company')
            ->where('user_id', $user->id)
            ->orderByDesc('login_time')
            ->get();

        $shiftCounts = Shift::where('user_id', $user->id)
            ->selectRaw('company_id, COUNT(*) as total_shifts')
            ->groupBy('company_id')
            ->pluck('total_shifts', 'company_id');

        $rates = $user->companies->pluck('pivot.hourly_rate', 'id');

        return view('staff.timesheet', compact('logins', 'rates', 'shiftCounts', 'user'));
    }

    public function timesheetDetails(StaffLogin $login)
    {
        // Ensure user can only view their own records or admin can view any
        $authUser = Auth::user();
        if (!$authUser->hasRole('Admin') && $login->user_id !== $authUser->id) {
            abort(403, 'Unauthorized access');
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $login->id,
                'date' => $login->login_time->format('F d, Y'),
                'login_time' => $login->login_time->format('g:i A'),
                'logout_time' => $login->logout_time ? $login->logout_time->format('g:i A') : 'Still Active',
                'company' => $login->company->name ?? 'Unknown',
                'status' => ucfirst($login->status ?? 'Present'),
                'shift_status' => ucfirst($login->shift_status ?? 'Completed'),
                'rate_type' => ucfirst($login->rate_type),
                'total_work_hours' => $login->work_hours ? round($login->work_hours, 2) . ' hours' : 'N/A',
                'total_pause_minutes' => $login->total_pause_minutes . ' minutes',
                'pause_periods' => $login->pause_periods ?? [],
                'current_pause_reason' => $login->current_pause_reason,
                'is_active' => $login->logout_time === null
            ]
        ]);
    }
}
