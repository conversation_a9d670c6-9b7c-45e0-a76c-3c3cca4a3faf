/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .swal2-popup {
  background-color: #0e1726 !important;
}
body.dark .swal2-title {
  color: #bfc9d4;
}
body.dark .swal2-html-container {
  color: #e95f2b;
}
body.dark .swal2-styled.swal2-default-outline:focus, body.dark .swal2-styled.swal2-confirm:focus {
  box-shadow: none;
}
body.dark .swal2-icon.swal2-success .swal2-success-ring {
  border-color: #0c272b;
}
body.dark .swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: #00ab55;
}
body.dark .swal2-icon.swal2-error {
  border-color: #2c1c2b;
}
body.dark .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
  background-color: #e7515a;
}
body.dark .swal2-icon.swal2-warning {
  border-color: #282625;
  color: #e2a03f;
}
body.dark .swal2-icon.swal2-info {
  border-color: #0b2f52;
  color: #2196f3;
}
body.dark .swal2-icon.swal2-question {
  border-color: #1d1a3b;
  color: #805dca;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
