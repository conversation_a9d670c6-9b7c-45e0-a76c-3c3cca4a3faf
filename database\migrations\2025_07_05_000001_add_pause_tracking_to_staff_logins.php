<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            $table->enum('shift_status', ['active', 'paused', 'ended'])->default('active')->after('status');
            $table->json('pause_periods')->nullable()->after('shift_status');
            $table->integer('total_pause_minutes')->default(0)->after('pause_periods');
            $table->text('current_pause_reason')->nullable()->after('total_pause_minutes');
            $table->timestamp('current_pause_start')->nullable()->after('current_pause_reason');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            $table->dropColumn([
                'shift_status',
                'pause_periods',
                'total_pause_minutes',
                'current_pause_reason',
                'current_pause_start'
            ]);
        });
    }
};
