<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\Shift;

class SendShiftReminder implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $now = now();
        $reminderWindow = $now->copy()->addHour()->format('Y-m-d H:i');

        $shifts = Shift::with('user')->where('start_time', $reminderWindow)->get();

        foreach ($shifts as $shift) {
            $user = $shift->user;

            // You can use Mail, SMS, Notification etc.
            $user->notify(new \App\Notifications\ShiftReminderNotification($shift));
        }
    }
}
