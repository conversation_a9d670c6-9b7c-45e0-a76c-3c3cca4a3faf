@extends('layouts.noauth')

@push('styles')
    <link href="{{ asset('src/assets/css/dark/authentication/auth-boxed.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('src/assets/css/light/authentication/auth-boxed.css') }}" rel="stylesheet" type="text/css" />

    {{-- Select2 CSS and Bootstrap 5 theme (optional) --}}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .repeat-bg-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 300px;
            transform: translate(-50%, -50%);
            background-image: url('/images/logo-dim.png');
            background-repeat: repeat;
            background-position: center;
            background-size: auto;
            z-index: 0;
        }

        .sign-in {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 32px;
        }

        .sign-in-btn {
            background-color: #E9A852;
            border-color: #E9A852;
            color: white;
            height: 56px;
        }

        .sign-in-btn:hover {
            background-color: #E9A852;
            border-color: #E9A852;
            color: white;
        }

        .card-shadow {
            box-shadow: 0px 24px 60px 0px #00000026;
        }

        #login-form input[type="email"],
        #login-form input[type="password"] {
            height: 56px !important;
        }

        .form-control.pr-5 {
            padding-right: 3rem !important;
        }

        /* Adjust icon positioning if needed */
        .position-absolute {
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
@endpush

@section('content')
    <div class="auth-container d-flex position-relative">
        <div class="repeat-bg-center"></div>
        <div class="container mx-auto align-self-center">
            <div class="row">
                <div class="col-xxl-4 col-xl-5 col-lg-5 col-md-8 col-12 d-flex flex-column align-self-center mx-auto">
                    <div class="card mt-3 mb-3 bg-white card-shadow rounded-xl border-0" style="border-radius: 16px">
                        <div class="card-body">
                            <form method="POST" action="{{ route('login.custom') }}" id="login-form">
                                @csrf
                                <div class="row">
                                    <div class="col-md-12 mb-3 d-flex justify-content-center">
                                        <img src="{{ asset('images/logo.png') }}" class="img-fluid" alt="logo">
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <h2 class="sign-in">Sign In</h2>
                                        <p>Access your dashboard and stay connected with Lets out source.</p>
                                    </div>

                                    {{-- Email --}}
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input id="email" type="email"
                                                class="form-control @error('email') is-invalid @enderror" name="email"
                                                value="{{ old('email') }}" required autocomplete="email" autofocus>
                                            @error('email')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- Password --}}
                                    <div class="col-12">
                                        <div class="mb-4">
                                            <label class="form-label" for="password">Password</label>
                                            <div class="input-group">
                                                <input id="password" type="password" name="password"
                                                    class="form-control @error('password') is-invalid @enderror" required
                                                    autocomplete="current-password">

                                                <span class="position-absolute end-0 top-50 translate-middle-y me-3"
                                                    style="cursor: pointer;" onclick="togglePasswordVisibility()">
                                                    <!-- Eye icon (default) -->
                                                    <img id="eye-icon" src="{{ asset('images/eye.png') }}" width="20"
                                                        height="20" alt="Show Password">
                                                    <!-- Eye-slash icon (hidden initially) -->
                                                    <img id="eye-slash-icon" src="{{ asset('images/eye-lash.png') }}"
                                                        width="20" height="20" alt="Hide Password"
                                                        style="display: none;">
                                                </span>

                                                @error('password')
                                                    <span class="invalid-feedback d-block" role="alert">
                                                        <strong>{{ $message }}</strong>
                                                    </span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    {{-- Remember Me --}}
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <div class="form-check form-check-primary form-check-inline">
                                                <input class="form-check-input" type="checkbox" id="form-check-default"
                                                    name="remember" {{ old('remember') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="form-check-default">
                                                    Remember me
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    {{-- Sign In Button --}}
                                    <div class="col-12">
                                        <div class="mb-4">
                                            <button class="btn sign-in-btn w-100">SIGN IN</button>
                                        </div>
                                    </div>

                                    {{-- Sign Up --}}
                                    <div class="col-12">
                                        <div class="text-center">
                                            <p class="mb-0">Don't have an account? <a href="{{ route('register.form') }}"
                                                    class="text-warning">Sign Up</a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        function togglePasswordVisibility() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            const eyeSlashIcon = document.getElementById('eye-slash-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.style.display = 'none';
                eyeSlashIcon.style.display = 'block';
            } else {
                passwordField.type = 'password';
                eyeIcon.style.display = 'block';
                eyeSlashIcon.style.display = 'none';
            }
        }
    </script>
@endpush
