<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            $table->foreignId('shift_id')->nullable()->constrained()->after('company_id');
            $table->enum('status', ['on_time', 'late_in', 'left_early', 'absent'])->nullable()->after('rate_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            //
        });
    }
};
