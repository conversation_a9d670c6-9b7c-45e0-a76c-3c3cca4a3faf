<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Send shift reminders every 30 minutes
        $schedule->job(new \App\Jobs\SendShiftReminders)->everyThirtyMinutes();

        // Keep the existing shift reminder job if needed
        $schedule->job(new \App\Jobs\SendShiftReminder)->everyMinute();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
