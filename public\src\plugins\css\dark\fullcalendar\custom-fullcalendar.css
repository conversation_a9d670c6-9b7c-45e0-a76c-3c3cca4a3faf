/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .calendar-container {
  padding: 30px 30px;
  background-color: #0e1726;
  box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
  border-radius: 8px;
  border: 1px solid #0e1726;
}
body.dark .fc .fc-button-primary {
  background-color: #1b2e4b;
  border-color: #1b2e4b;
  letter-spacing: 1px;
  font-size: 14px;
  color: #fff;
}
body.dark .fc .fc-button-primary:not(:disabled).fc-button-active {
  background-color: #805dca;
  font-weight: 900;
  border-color: #1b2e4b;
}
body.dark .fc .fc-button-primary:hover, body.dark .fc .fc-button-primary:not(:disabled):active {
  background-color: #191e3a;
  color: #fff;
  border-color: #1b2e4b;
}
body.dark .fc .fc-button-primary:focus, body.dark .fc .fc-button-primary:active:focus {
  box-shadow: none !important;
}
body.dark .fc .fc-list-sticky .fc-list-day > * {
  background-color: #0e1726;
}
body.dark .fc .fc-daygrid-body {
  width: 100% !important;
}
body.dark .fc .fc-scrollgrid-section table {
  width: 100% !important;
}
body.dark .fc .fc-scrollgrid-section-body table {
  width: 100% !important;
}
body.dark .fc-theme-standard .fc-list-day-cushion {
  background-color: #0e1726;
}
body.dark .fc-theme-standard .fc-list {
  border: 1px solid #3b3f5c;
}
body.dark .fc .fc-button {
  border-radius: 8px;
  padding: 7px 20px;
  text-transform: capitalize;
}
body.dark .fc .fc-addEventButton-button {
  background-color: #4361ee;
  border-color: #4361ee;
  color: #fff;
  font-weight: 700;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59);
}
body.dark .fc .fc-addEventButton-button:hover, body.dark .fc .fc-addEventButton-button:not(:disabled):active {
  background-color: #4361ee;
  border-color: #4361ee;
  box-shadow: none;
}
body.dark .fc-theme-standard .fc-scrollgrid, body.dark .fc-theme-standard td, body.dark .fc-theme-standard th {
  border: 1px solid #3b3f5c;
}
body.dark .fc .fc-list-table tr > * {
  border-left: 0;
  border-right: 0;
}
body.dark .fc-v-event .fc-event-main {
  color: #3b3f5c;
}
body.dark .fc-v-event .fc-event-main .fc-event-main-frame .fc-event-time {
  color: #e0e6ed;
}
body.dark .fc-v-event .fc-event-main .fc-event-main-frame .fc-event-title-container {
  color: #e0e6ed;
}
body.dark .fc-timegrid-event-harness-inset .fc-timegrid-event, body.dark .fc-timegrid-event.fc-event-mirror, body.dark .fc-timegrid-more-link {
  box-shadow: none;
}
body.dark .event-fc-color {
  background-color: #1b2e4b;
  border: none;
  padding: 4px 10px;
  margin-bottom: 1px;
  font-size: 13px;
  letter-spacing: 1px;
  font-weight: 300;
  cursor: pointer;
}
body.dark .event-fc-color:hover {
  background-color: #060818;
}
body.dark .fc .fc-daygrid-day.fc-day-today {
  background-color: transparent;
  padding: 3px;
  border-radius: 23px;
}
body.dark .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-frame {
  background-color: #1b2e4b;
  border-radius: 8px;
}
body.dark .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-frame .fc-daygrid-day-number {
  font-size: 15px;
  font-weight: 800;
}
body.dark .fc-daygrid-event-dot {
  margin: 0 6px 0 0;
}
body.dark .fc-bg-primary {
  color: #4361ee;
  background-color: rgba(67, 97, 238, 0.15);
}
body.dark .fc-bg-primary.fc-h-event .fc-event-main {
  color: #4361ee;
}
body.dark .fc-bg-success {
  color: #00ab55;
  background-color: rgba(26, 188, 156, 0.15);
}
body.dark .fc-bg-success.fc-h-event .fc-event-main {
  color: #00ab55;
}
body.dark .fc-bg-warning {
  color: #e2a03f;
  background-color: rgba(226, 160, 63, 0.15);
}
body.dark .fc-bg-warning.fc-h-event .fc-event-main {
  color: #e2a03f;
}
body.dark .fc-bg-danger {
  color: #e7515a;
  background-color: rgba(231, 81, 90, 0.15);
}
body.dark .fc-bg-danger.fc-h-event .fc-event-main {
  color: #e7515a;
}
body.dark .fc-bg-primary .fc-daygrid-event-dot {
  border-color: #4361ee;
}
body.dark .fc-bg-success .fc-daygrid-event-dot {
  border-color: #00ab55;
}
body.dark .fc-bg-warning .fc-daygrid-event-dot {
  border-color: #e2a03f;
}
body.dark .fc-bg-danger .fc-daygrid-event-dot {
  border-color: #e7515a;
}
body.dark .fc .fc-list-event:hover td {
  background-color: #060818;
}

/* Modal CSS */
body.dark .btn-update-event {
  display: none;
}
@media (max-width: 1199px) {
  body.dark .calendar-container {
    padding: 30px 0 0 0;
  }
  body.dark .fc-theme-standard .fc-list {
    border: none;
  }
  body.dark .fc .fc-toolbar {
    align-items: center;
    flex-direction: column;
  }
  body.dark .fc-toolbar-chunk:not(:first-child) {
    margin-top: 35px;
  }
  body.dark .fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 50px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
