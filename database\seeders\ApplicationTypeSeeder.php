<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ApplicationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('application_types')->insert([
            ['name' => 'Leave Application',       'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Salary Application',     'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Casual Leave',     'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Complaint',  'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Request for Resource',      'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Attendance Application',      'created_at' => now(), 'updated_at' => now()],

        ]);
    }
}
