@extends('layouts.app')

@push('styles')
<style>
    .shift-management-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .management-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .card-header-custom {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 1.5rem 2rem;
    }

    .filters-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .btn-orange {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }

    .btn-orange:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(233, 168, 82, 0.3);
        color: white;
    }

    .shift-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
    }

    .duration-display {
        font-weight: 600;
        color: #E9A852;
    }
</style>
@endpush

@section('content')
<div class="shift-management-container">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="management-card">
            <div class="card-header-custom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h3 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Shift Management</h3>
                        <p class="mb-0 opacity-75">Manage and assign shifts to staff members</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="{{ route('shifts.create') }}" class="btn btn-light me-2">
                            <i class="fas fa-plus me-1"></i>New Shift
                        </a>
                        <a href="{{ route('shifts.bulk-create') }}" class="btn btn-light">
                            <i class="fas fa-users-cog me-1"></i>Bulk Assign
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="management-card">
            <div class="card-body">
                <div class="filters-section">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filters</h5>
                    <form method="GET" action="{{ route('shifts.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Company</label>
                                    <select name="company_id" class="form-control">
                                        <option value="">All Companies</option>
                                        @foreach ($companies as $company)
                                            <option value="{{ $company->id }}" {{ request('company_id') == $company->id ? 'selected' : '' }}>
                                                {{ $company->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Staff Member</label>
                                    <select name="user_id" class="form-control">
                                        <option value="">All Staff</option>
                                        @foreach ($users as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="scheduled" {{ request('status') == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">From Date</label>
                                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="form-label">To Date</label>
                                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-orange">
                                    <i class="fas fa-search me-1"></i>Apply Filters
                                </button>
                                <a href="{{ route('shifts.index') }}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                                <button type="button" class="btn btn-info ms-2" onclick="sendReminders()">
                                    <i class="fas fa-bell me-1"></i>Send Reminders
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Shifts Table -->
                <div class="table-responsive">
                    <table id="shift-table" class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Staff Member</th>
                                <th>Company</th>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th>Duration</th>
                                <th>Type</th>
                                <th>Rate</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($shifts as $shift)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user-circle me-2 text-muted"></i>
                                            {{ $shift->user->name }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $shift->company->name }}</span>
                                    </td>
                                    <td>{{ $shift->start_time->format('M d, Y H:i') }}</td>
                                    <td>{{ $shift->end_time->format('M d, Y H:i') }}</td>
                                    <td>
                                        <span class="duration-display">{{ $shift->formatted_duration }}</span>
                                    </td>
                                    <td>
                                        <span class="shift-type-badge badge bg-{{ $shift->shift_type === 'regular' ? 'success' : ($shift->shift_type === 'overtime' ? 'warning' : 'info') }}">
                                            {{ ucfirst($shift->shift_type) }}
                                        </span>
                                    </td>
                                    <td>£{{ number_format($shift->hourly_rate, 2) }}/hr</td>
                                    <td>
                                        <span class="badge bg-{{ $shift->status === 'active' ? 'success' : ($shift->status === 'scheduled' ? 'primary' : ($shift->status === 'completed' ? 'secondary' : 'danger')) }}">
                                            {{ ucfirst($shift->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($shift->createdBy)
                                            <small class="text-muted">{{ $shift->createdBy->name }}</small>
                                        @else
                                            <small class="text-muted">System</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('shifts.show', $shift->id) }}" class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('shifts.edit', $shift->id) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('shifts.destroy', $shift->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this shift?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $shifts->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendReminders() {
    if (confirm('Send shift reminders to all staff with upcoming shifts?')) {
        fetch('{{ route("shifts.send-reminders") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('Error sending reminders');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending reminders');
        });
    }
}
</script>
@endsection

@push('scripts')
    <script src="../src/plugins/src/table/datatable/datatables.js"></script>
    <script>
        $('#shift-table').DataTable({
            "dom": "<'dt--top-section'<'row'<'col-12 col-sm-6 d-flex justify-content-sm-start justify-content-center'l><'col-12 col-sm-6 d-flex justify-content-sm-end justify-content-center mt-sm-0 mt-3'f>>>" +
                "<'table-responsive'tr>" +
                "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count mb-sm-0 mb-3'i><'dt--pagination'p>>",
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-arrow-left">
                        <line x1="19" y1="12" x2="5" y2="12"></line>
                        <polyline points="12 19 5 12 12 5"></polyline></svg>`,
                    "sNext": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-arrow-right">
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                        <polyline points="12 5 19 12 12 19"></polyline></svg>`
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-search"><circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>`,
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
                "sZeroRecords": "No shifts found."
            },
            "stripeClasses": [],
            "lengthMenu": [7, 10, 20, 50],
            "pageLength": 10
        });
    </script>
@endpush
