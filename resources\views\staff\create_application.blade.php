{{-- @extends('layouts.app')

@section('content')
<div class="container mt-4">
    <h3>Applications</h3>
    <p>You can apply for leaves or shift changes here.</p>
</div>
@endsection --}}

@extends('layouts.app')

@push('styles')
    {{-- Select2 --}}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@section('content')
<div class="container mt-4">
    <div class="card p-4 shadow-sm">
        <h4 class="mb-4">New Application</h4>
        <form action="{{ route('applications.store') }}" method="POST">
            @csrf

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="application_type_id" class="form-label">Application Type</label>
                    <select name="application_type_id" id="application_type_id" class="form-control select2" required>
                        <option value="">Select Application Type</option>
                        @foreach($applicationTypes as $type)
                            <option value="{{ $type->id }}" {{ old('application_type_id') == $type->id ? 'selected' : '' }}>
                                {{ $type->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-4">
                    <label for="title" class="form-label">Title</label>
                    <input name="title" id="title" class="form-control" value="{{ old('title') }}" required>
                </div>

                <div class="col-md-4">
                    <label for="application_date" class="form-label">Date</label>
                    <input type="date" name="application_date" id="application_date" class="form-control"
                        value="{{ old('application_date') ?? now()->toDateString() }}" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea name="description" id="description" class="form-control" rows="4" required>{{ old('description') }}</textarea>
            </div>

            <button type="submit" class="btn btn-warning w-100">Submit</button>
        </form>
    </div>
</div>
@endsection

@push('scripts')
    {{-- jQuery + Select2 --}}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function () {
            $('.select2').select2({
                placeholder: "Select Application Type",
                width: '100%'
            });
        });
    </script>
@endpush
