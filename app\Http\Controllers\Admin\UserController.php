<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\Company;
use App\Models\Shift;
use App\Models\StaffLogin;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Auth;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with(['companies', 'roles'])
            ->latest()
            ->get();
        return view('Admin.User.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $companies = Company::all();
        $roles = Role::all();
        return view('Admin.User.create', compact('companies', 'roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6|confirmed',
            'company_ids' => 'required|array|min:1',
            'role' => 'required|exists:roles,name',
            'hourly_rate' => 'nullable',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
        ]);

        // $user->companies()->sync($validated['company_ids']);
        // Then attach companies with hourly rate as pivot data
        $syncData = [];
        foreach ($validated['company_ids'] as $companyId) {
            $syncData[$companyId] = ['hourly_rate' => $validated['hourly_rate']];
        }

        $user->companies()->sync($syncData);
        $user->assignRole($validated['role']);

        return redirect()->route('users.index')->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::with(['companies', 'roles'])->findOrFail($id);
        $companies = Company::all();
        $roles = Role::all();
        return view('Admin.User.edit', compact('user', 'companies', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'email' => 'required|email|unique:users,email,' . $id,
            'company_ids' => 'required|array|min:1',
            'role' => 'required|exists:roles,name',
            'password' => 'nullable|min:6|confirmed',
            'hourly_rate' => 'nullable|numeric',

        ]);

        $user = User::findOrFail($id);
        $user->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => $validated['password'] ? Hash::make($validated['password']) : $user->password,
        ]);

        // $user->companies()->sync($validated['company_ids']);
        $syncData = [];
        foreach ($validated['company_ids'] as $companyId) {
            $syncData[$companyId] = ['hourly_rate' => $validated['hourly_rate']];
        }

        $user->companies()->sync($syncData);
        $user->syncRoles([$validated['role']]);

        return redirect()->route('users.index')->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = User::findOrFail($id);
        $user->companies()->detach();
        $user->syncRoles([]);
        $user->delete();

        return redirect()->route('users.index')->with('success', 'User deleted successfully.');
    }

    public function dashboard()
    {
        $user = Auth::user();

        if ($user->hasRole('Admin')) {
            // Fetch dashboard statistics
            $totalUsers = User::count();
            $totalStaff = User::role('Staff')->count();
            $totalTeamLeaders = User::role('Team Leader')->count();
            $totalCompanies = Company::count();
            $activeCompanies = Company::where('status', 'active')->count();

            // Shift statistics
            $totalShifts = Shift::count();
            $todayShifts = Shift::whereDate('start_time', today())->count();
            $upcomingShifts = Shift::where('start_time', '>', now())->count();
            $completedShifts = Shift::where('status', 'completed')->count();
            $missedShifts = Shift::where('status', 'missed')->count();

            // Staff login statistics
            $activeStaffLogins = StaffLogin::whereNull('logout_time')->count();
            $todayLogins = StaffLogin::whereDate('login_time', today())->count();

            // Application statistics
            $totalApplications = Application::count();
            $pendingApplications = Application::where('status', 'pending')->count();
            $approvedApplications = Application::where('status', 'approved')->count();
            $rejectedApplications = Application::where('status', 'rejected')->count();

            // Recent activities
            $recentShifts = Shift::with(['user', 'company'])
                ->latest()
                ->limit(5)
                ->get();

            $recentApplications = Application::with(['user', 'applicationType'])
                ->latest()
                ->limit(5)
                ->get();

            $recentLogins = StaffLogin::with(['user', 'company'])
                ->whereDate('login_time', today())
                ->latest()
                ->limit(5)
                ->get();

            return view('dashboard.admin', compact(
                'totalUsers', 'totalStaff', 'totalTeamLeaders', 'totalCompanies', 'activeCompanies',
                'totalShifts', 'todayShifts', 'upcomingShifts', 'completedShifts', 'missedShifts',
                'activeStaffLogins', 'todayLogins',
                'totalApplications', 'pendingApplications', 'approvedApplications', 'rejectedApplications',
                'recentShifts', 'recentApplications', 'recentLogins'
            ));
        }

        if ($user->hasRole('Team Leader')) {
            return view('dashboard.team_leader');
        }

        if ($user->hasRole('Staff')) {
            $companyIds = $user->companies->pluck('id')->toArray();
            $rateType = match (count($companyIds)) {
                2           => 'double',
                3, 4, 5     => 'triple',
                default     => 'regular',
            };

            // Note: Automatic StaffLogin creation is now handled by the enhanced shift management system
            // Staff users must explicitly start their shifts using the new shift management interface

            // Get staff dashboard statistics using StaffLogin records
            $currentShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->whereIn('shift_status', ['active', 'paused'])
                ->first();

            $todayShifts = StaffLogin::where('user_id', $user->id)
                ->whereDate('login_time', today())
                ->count();

            // For upcoming shifts, check if there are any scheduled Shift records
            $upcomingShifts = Shift::where('user_id', $user->id)
                ->where('start_time', '>', now())
                ->count();

            $totalShifts = StaffLogin::where('user_id', $user->id)->count();

            $activeLogin = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->first();

            $todayWorkHours = StaffLogin::where('user_id', $user->id)
                ->whereDate('login_time', today())
                ->whereNotNull('logout_time')
                ->get()
                ->sum(function ($login) {
                    return $login->logout_time->diffInMinutes($login->login_time);
                });

            $totalApplications = Application::where('user_id', $user->id)->count();
            $pendingApplications = Application::where('user_id', $user->id)
                ->where('status', 'pending')
                ->count();

            // Get recent shifts from StaffLogin records (new shift management system)
            $recentShifts = StaffLogin::where('user_id', $user->id)
                ->with('company')
                ->latest('login_time')
                ->take(5)
                ->get()
                ->map(function ($login) {
                    // Transform StaffLogin to look like Shift for compatibility
                    return (object) [
                        'company' => $login->company,
                        'start_time' => $login->login_time,
                        'end_time' => $login->logout_time,
                        'status' => $login->shift_status ?? 'completed'
                    ];
                });

            return view('dashboard.staff', compact(
                'currentShift', 'todayShifts', 'upcomingShifts', 'totalShifts',
                'activeLogin', 'todayWorkHours', 'totalApplications', 'pendingApplications',
                'recentShifts'
            ));
        }

        abort(403, 'Unauthorized access');
    }
}
