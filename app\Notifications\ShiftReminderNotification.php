<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Shift;

class ShiftReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $shift;
    /**
     * Create a new notification instance.
     */
    public function __construct(Shift $shift)
    {
        $this->shift = $shift;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $startTime = $this->shift->start_time->format('l, F j, Y \a\t g:i A');
        $company = $this->shift->company->name;
        $duration = $this->shift->formatted_duration;

        return (new MailMessage)
            ->subject('🔔 Shift Reminder - ' . $company)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a friendly reminder about your upcoming shift:')
            ->line('**Company:** ' . $company)
            ->line('**Start Time:** ' . $startTime)
            ->line('**Duration:** ' . $duration)
            ->line('**Shift Type:** ' . ucfirst($this->shift->shift_type))
            ->when($this->shift->notes, function ($message) {
                return $message->line('**Notes:** ' . $this->shift->notes);
            })
            ->action('View Dashboard', route('dashboard'))
            ->line('Please ensure you arrive on time and are prepared for your shift.')
            ->line('Thank you for your dedication!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'shift_id' => $this->shift->id,
            'company_name' => $this->shift->company->name,
            'start_time' => $this->shift->start_time->toISOString(),
            'end_time' => $this->shift->end_time->toISOString(),
            'shift_type' => $this->shift->shift_type,
            'duration' => $this->shift->formatted_duration,
            'message' => 'You have an upcoming shift at ' . $this->shift->company->name . ' starting at ' . $this->shift->start_time->format('g:i A'),
        ];
    }
}
