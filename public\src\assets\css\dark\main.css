@charset "UTF-8";
/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
html {
  min-height: 100%;
}

body.dark {
  color: #888ea8;
  height: 100%;
  font-size: 0.875rem;
  background: #f1f2f3;
  overflow-x: hidden;
  overflow-y: auto;
  letter-spacing: 0.0312rem;
  font-family: "Nunito", sans-serif;
}
body.dark h1, body.dark h2, body.dark h3, body.dark h4, body.dark h5, body.dark h6 {
  color: #e0e6ed;
}
body.dark a {
  text-decoration: none;
  background-color: transparent;
}

:focus {
  outline: none;
}

body.dark .dark-element {
  display: block;
}

.dark-element {
  display: none;
}

body.dark .light-element {
  display: none;
}

.light-element {
  display: block;
}

body.dark p {
  margin-top: 0;
  margin-bottom: 0.625rem;
  color: #e0e6ed;
}
body.dark hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border-top: 1px solid #f1f2f3;
}
body.dark strong {
  font-weight: 600;
}
body.dark code {
  color: #e7515a;
}
body.dark select.form-custom::-ms-expand {
  display: none;
}
body.dark .custom-file-input:focus ~ .custom-file-label {
  border: 1px solid #3b3f5c;
  box-shadow: none;
}
body.dark .custom-file-input:focus ~ .custom-file-label::after {
  border: none;
  border-left: 1px solid #3b3f5c;
}
body.dark .lead a.btn.btn-primary.btn-lg {
  margin-top: 15px;
  border-radius: 4px;
}
body.dark .jumbotron {
  background-color: #1b2e4b;
}
body.dark .mark, body.dark mark {
  background-color: #bfc9d4;
}
body.dark .modal-content {
  background: #0e1726;
}
body.dark .code-section-container {
  margin-top: 20px;
  text-align: left;
}
body.dark .toggle-code-snippet {
  border: none;
  background-color: transparent !important;
  padding: 0px !important;
  box-shadow: none !important;
  color: #888ea8 !important;
  margin-bottom: -24px;
  border-bottom: 1px dashed #bfc9d4;
  border-radius: 0 !important;
}
body.dark .toggle-code-snippet svg {
  color: #888ea8;
}
body.dark .toggle-code-snippet .toggle-code-icon {
  width: 16px;
  height: 16px;
  transition: 0.3s;
  transform: rotate(-90deg);
  vertical-align: text-top;
}
body.dark .code-section-container.show-code .toggle-code-snippet .toggle-code-icon {
  transform: rotate(0deg);
}
body.dark .code-section {
  padding: 0;
  height: 0;
}
body.dark .code-section-container.show-code .code-section {
  margin-top: 20px;
  height: auto;
}
body.dark .code-section pre {
  margin-bottom: 0;
  height: 0;
  padding: 0;
  border-radius: 6px;
}
body.dark .code-section-container.show-code .code-section pre {
  height: auto;
  padding: 22px;
}
body.dark .code-section code {
  color: #fff;
}
body.dark .media {
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}
body.dark .media-body {
  -ms-flex: 1;
  flex: 1;
}
body.dark blockquote.blockquote {
  color: #009688;
  padding: 20px 20px 20px 14px;
  font-size: 0.875rem;
  background-color: #060818;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
  border: 1px solid #060818;
  border-left: 2px solid #009688;
}
body.dark blockquote.blockquote > p {
  margin-bottom: 0;
}
body.dark blockquote .small:before, body.dark blockquote footer:before, body.dark blockquote small:before {
  content: "— ";
}
body.dark blockquote .small, body.dark blockquote footer, body.dark blockquote small {
  display: block;
  font-size: 80%;
  line-height: 1.42857143;
  color: #888ea8;
}
body.dark blockquote.media-object.m-o-border-right {
  border-right: 4px solid #009688;
  border-left: none;
}
body.dark blockquote.media-object .media .usr-img img {
  width: 55px;
}
body.dark .list-icon {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
}
body.dark .list-icon li:not(:last-child) {
  margin-bottom: 15px;
}
body.dark .list-icon svg {
  width: 18px;
  height: 18px;
  color: #2196f3;
  margin-right: 2px;
  vertical-align: sub;
}
body.dark .list-icon .list-text {
  font-size: 14px;
  font-weight: 600;
  color: #bfc9d4;
  letter-spacing: 1px;
}
body.dark a {
  color: #e0e6ed;
  outline: none;
}
body.dark a:hover {
  color: #bfc9d4;
  text-decoration: none;
}
body.dark a:focus {
  outline: none;
  text-decoration: none;
}
body.dark button:focus {
  outline: none;
}
body.dark textarea {
  outline: none;
}
body.dark textarea:focus {
  outline: none;
}
body.dark .btn-link:hover {
  text-decoration: none;
}
body.dark span.blue {
  color: #4361ee;
}
body.dark span.green {
  color: #00ab55;
}
body.dark span.red {
  color: #e7515a;
}
body.dark .card {
  border: 1px solid #191e3a;
  border-radius: 10px;
  background: #191e3a;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .card-img, body.dark .card-img-top {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
body.dark .card {
  /* Card Style 2 */
  /* Card Style 3 */
  /* Card Style 4 */
  /* Card Style 5 */
  /* Card Style 6 */
  /* Card Style 7 */
}
body.dark .card .card-header {
  color: #fff;
  border-bottom: 1px solid #3b3f5c;
  padding: 12px 20px;
}
body.dark .card .card-footer {
  border-top: 1px solid #3b3f5c;
  padding: 12px 20px;
  background-color: transparent;
}
body.dark .card .card-body {
  padding: 24px 20px;
}
body.dark .card .card-title {
  color: #fff;
  line-height: 1.5;
}
body.dark .card .card-text {
  color: #d3d3d3;
}
body.dark .card .media img.card-media-image {
  border-radius: 50%;
  width: 45px;
  height: 45px;
}
body.dark .card .media .media-body .media-heading {
  font-size: 14px;
  font-weight: 500;
}
body.dark .card.bg-primary .card-title {
  color: #fff;
}
body.dark .card.bg-primary .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-primary p {
  color: #e0e6ed;
}
body.dark .card.bg-primary a {
  color: #bfc9d4;
}
body.dark .card.bg-info .card-title {
  color: #fff;
}
body.dark .card.bg-info .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-info p {
  color: #e0e6ed;
}
body.dark .card.bg-info a {
  color: #bfc9d4;
}
body.dark .card.bg-success .card-title {
  color: #fff;
}
body.dark .card.bg-success .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-success p {
  color: #e0e6ed;
}
body.dark .card.bg-success a {
  color: #bfc9d4;
}
body.dark .card.bg-warning .card-title {
  color: #fff;
}
body.dark .card.bg-warning .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-warning p {
  color: #e0e6ed;
}
body.dark .card.bg-warning a {
  color: #bfc9d4;
}
body.dark .card.bg-danger .card-title {
  color: #fff;
}
body.dark .card.bg-danger .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-danger p {
  color: #e0e6ed;
}
body.dark .card.bg-danger a {
  color: #bfc9d4;
}
body.dark .card.bg-secondary .card-title {
  color: #fff;
}
body.dark .card.bg-secondary .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-secondary p {
  color: #e0e6ed;
}
body.dark .card.bg-secondary a {
  color: #bfc9d4;
}
body.dark .card.bg-dark .card-title {
  color: #fff;
}
body.dark .card.bg-dark .card-text {
  color: #e0e6ed;
}
body.dark .card.bg-dark p {
  color: #e0e6ed;
}
body.dark .card.bg-dark a {
  color: #bfc9d4;
}
body.dark .card.style-2 {
  padding: 15px 18px;
  border-radius: 15px;
}
body.dark .card.style-2 .card-img, body.dark .card.style-2 .card-img-top {
  border-radius: 15px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .card.style-3 {
  padding: 10px 10px;
  border-radius: 15px;
  flex-direction: row;
}
body.dark .card.style-3 .card-img, body.dark .card.style-3 .card-img-top {
  border-radius: 15px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  width: 50%;
  margin-right: 25px;
}
body.dark .card.style-4 .media img.card-media-image {
  width: 55px;
  height: 55px;
}
body.dark .card.style-4 .media .media-body .media-heading {
  font-size: 16px;
}
body.dark .card.style-4 .media .media-body .media-text {
  font-size: 14px;
}
body.dark .card.style-4 .progress {
  background-color: #060818;
}
body.dark .card.style-4 .attachments {
  cursor: pointer;
}
body.dark .card.style-4 .attachments:hover {
  color: #00ab55;
}
body.dark .card.style-4 .attachments svg {
  width: 18px;
  height: 18px;
  stroke-width: 1.6;
}
body.dark .card.style-5 {
  flex-direction: row;
}
body.dark .card.style-5 .card-top-content {
  padding: 24px 0 24px 20px;
}
body.dark .card.style-5 .card-content {
  -ms-flex: 1;
  flex: 1;
}
body.dark .card.style-6 .badge:not(.badge-dot) {
  position: absolute;
  right: 8px;
  top: 8px;
}
body.dark .card.style-7 .card-img-top {
  border-radius: 10px;
}
body.dark .card.style-7 .card-header {
  position: absolute;
  width: 100%;
  top: 0;
  border: none;
  background-color: rgba(0, 0, 0, 0.3803921569);
  backdrop-filter: saturate(180%) blur(10px);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
body.dark .card.style-7 .card-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  border: none;
  background-color: rgba(0, 0, 0, 0.3803921569);
  backdrop-filter: saturate(180%) blur(10px);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
body.dark .card.style-7 .card-title {
  color: #fff;
}
body.dark .card.style-7 .card-text {
  color: #e0e6ed;
}
body.dark .form-group label {
  font-size: 15px;
  color: #d3d3d3;
  letter-spacing: 1px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

@media (min-width: 1400px) {
  body.dark .container, .container-lg {
    max-width: 1440px;
  }
  body.dark .container-md, body.dark .container-sm, body.dark .container-xl, body.dark .container-xxl {
    max-width: 1440px;
  }
}
/* Media Object */
/*blockquote*/
/* Icon List */
/*      CARD    */
@media (max-width: 575px) {
  /* Card Style 3 */
  body.dark .card.style-3 {
    flex-direction: column;
  }
  body.dark .card.style-3 .card-img, body.dark .card.style-3 .card-img-top {
    width: 100%;
    height: auto;
    margin-bottom: 15px;
  }
}
/* clears the 'X' from Chrome */
input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  display: none;
}

/* clears the 'X' from Internet Explorer */
input[type=search]::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

input[type=search]::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

/*      Form Group Label       */
body.dark label {
  font-size: 15px;
  color: #888ea8;
  letter-spacing: 1px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

/*  Disable forms     */
body.dark .custom-control-input:disabled ~ .custom-control-label {
  color: #d3d3d3;
  cursor: no-drop;
}
body.dark .form-control:disabled:not(.flatpickr-input), body.dark .form-control[readonly]:not(.flatpickr-input) {
  background-color: #3b3f5c;
  cursor: no-drop;
  color: #d3d3d3;
}
body.dark .custom-control-input:disabled ~ .form-check-input, body.dark .custom-control-input[disabled] ~ .form-check-input {
  background-color: #3b3f5c;
  cursor: no-drop;
}
body.dark .form-control {
  height: auto;
  border: 1px solid #1b2e4b;
  color: #009688;
  font-size: 15px;
  padding: 8px 10px;
  letter-spacing: 1px;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  background: #1b2e4b;
  height: auto;
  transition: none;
}
body.dark .form-text {
  color: #fff;
}
body.dark .form-control[type=range] {
  padding: 0;
}
body.dark .form-control:focus {
  box-shadow: none;
  border-color: #3b3f5c;
  color: #22c7d5;
  background-color: #1b2e4b;
}
body.dark .form-control::placeholder {
  color: #888ea8 !important;
  font-size: 15px;
}
body.dark .form-control::-webkit-input-placeholder, body.dark .form-control::-ms-input-placeholder, body.dark .form-control::-moz-placeholder {
  color: #888ea8 !important;
  font-size: 15px;
}
body.dark .form-control:focus::placeholder {
  color: #bfc9d4 !important;
  font-size: 15px;
}
body.dark .form-control:focus::-webkit-input-placeholder, body.dark .form-control:focus::-ms-input-placeholder, body.dark .form-control:focus::-moz-placeholder {
  color: #bfc9d4 !important;
  font-size: 15px;
}
body.dark .form-control.form-control-lg {
  font-size: 19px;
  padding: 11px 20px;
}
body.dark .form-control.form-control-sm {
  padding: 7px 16px;
  font-size: 13px;
}
body.dark .form-select.form-control-sm {
  padding: 7px 16px;
  font-size: 13px;
}
body.dark .form-check {
  min-height: auto;
}
body.dark .form-check-input {
  background-color: #515365;
  border-color: #515365;
  width: 17px;
  height: 17px;
  margin-top: 0.21em;
  transition: background-color 0.15s ease-in-out, background-position 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
body.dark .form-check-input:focus {
  border-color: #515365;
  box-shadow: none;
}
body.dark .form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .form-check:not(.form-switch) .form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 11' width='13' height='11' fill='none'%3e%3cpath d='M11.0426 1.02893C11.3258 0.695792 11.8254 0.655283 12.1585 0.938451C12.4917 1.22162 12.5322 1.72124 12.249 2.05437L5.51985 9.97104C5.23224 10.3094 4.72261 10.3451 4.3907 10.05L0.828197 6.88335C0.50141 6.59288 0.471975 6.09249 0.762452 5.7657C1.05293 5.43891 1.55332 5.40948 1.88011 5.69995L4.83765 8.32889L11.0426 1.02893Z' fill='%23FFFFFF'/%3e%3c/svg%3e");
  background-size: 60% 60%;
}
body.dark .form-check .form-check-input {
  margin-left: -1.6em;
}

/*      Form Control       */
@supports (-webkit-overflow-scrolling: touch) {
  /* CSS specific to iOS devices */
  body.dark .form-control {
    color: #0e1726;
  }
}
/*      Custom Select       */
body.dark .form-check-input:checked[type=checkbox]:not([role=switch]) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 11' width='13' height='11' fill='none'%3e%3cpath d='M11.0426 1.02893C11.3258 0.695792 11.8254 0.655283 12.1585 0.938451C12.4917 1.22162 12.5322 1.72124 12.249 2.05437L5.51985 9.97104C5.23224 10.3094 4.72261 10.3451 4.3907 10.05L0.828197 6.88335C0.50141 6.59288 0.471975 6.09249 0.762452 5.7657C1.05293 5.43891 1.55332 5.40948 1.88011 5.69995L4.83765 8.32889L11.0426 1.02893Z' fill='%23FFFFFF'/%3e%3c/svg%3e");
  background-size: 60% 60%;
}

/*      Custom Select       */
body.dark .form-select {
  height: auto;
  font-size: 15px;
  padding: 0.75rem 1.25rem;
  letter-spacing: 1px;
  border: 1px solid #1b2e4b;
  color: #009688;
  background-color: #1b2e4b;
  border-radius: 6px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23009688' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  transition: none;
}
body.dark .form-select.form-select-lg {
  font-size: 19px;
  padding: 11px 20px;
}
body.dark .form-select.form-select-sm {
  padding: 7px 16px;
  font-size: 13px;
}
body.dark .form-select:focus {
  box-shadow: none;
  border-color: #3b3f5c;
  color: #22c7d5;
  background-color: #1b2e4b;
}
body.dark .form-control-file {
  width: 100%;
  color: #805dca;
}
body.dark .form-control-file::-webkit-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 12px;
  color: #fff;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #805dca;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border-radius: 4px;
  border: transparent;
  outline: none;
}
body.dark .form-control-file::-ms-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 14px;
  color: #fff;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #805dca;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border-radius: 4px;
  border: transparent;
  outline: none;
}
body.dark .form-control-file.form-control-file-rounded::-webkit-file-upload-button {
  -webkit-border-radius: 1.875rem !important;
  -moz-border-radius: 1.875rem !important;
  -ms-border-radius: 1.875rem !important;
  -o-border-radius: 1.875rem !important;
  border-radius: 1.875rem !important;
}
body.dark select.form-control.form-custom {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  vertical-align: middle;
  background: #fff url(../img/arrow-down.html) no-repeat right 0.75rem center;
  background-size: 13px 14px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
body.dark .file-upload-input {
  padding: 0.375rem 0.75rem;
}
body.dark .file-upload-input::-webkit-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 12px;
  color: #fff;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #1b2e4b;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border: transparent;
  outline: none;
}
body.dark .file-upload-input::-webkit-file-upload-button:hover {
  background-color: #1b2e4b;
}
body.dark .file-upload-input.form-control-file-rounded::-webkit-file-upload-button {
  -webkit-border-radius: 1.875rem !important;
  -moz-border-radius: 1.875rem !important;
  -ms-border-radius: 1.875rem !important;
  -o-border-radius: 1.875rem !important;
  border-radius: 1.875rem !important;
}
body.dark .form-control[type=file]::file-selector-button, body.dark .form-control[type=file]::-webkit-file-upload-button {
  background-color: #1b2e4b !important;
  color: #fff;
}
body.dark .input-group button:hover, body.dark .input-group .btn:hover, body.dark .input-group button:focus, body.dark .input-group .btn:focus {
  transform: none;
}
body.dark .input-group .dropdown-menu {
  border: none;
  z-index: 1028;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  right: auto;
  border-radius: 8px;
  background-color: #1b2e4b;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .input-group .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #bfc9d4;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}
body.dark .input-group .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
}
body.dark .input-group .dropdown-menu .dropdown-item:hover {
  color: #2196f3;
}
body.dark .input-group .dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #0e1726;
}
body.dark .input-group .input-group-text {
  border: 1px solid #1b2e4b;
  background-color: #191e3a;
  color: #888ea8;
}
body.dark .input-group .input-group-text svg {
  color: #888ea8;
}
body.dark .input-group:hover .input-group-text svg {
  color: #22c7d5;
  fill: rgba(37, 213, 228, 0.14);
}
body.dark .input-group .input-group-append:not(.btn) .input-group-text {
  border: 1px solid #1b2e4b;
  background-color: #191e3a;
  color: #888ea8;
}
body.dark .input-group .input-group-append:not(.btn) .input-group-text svg {
  color: #888ea8;
}
body.dark .input-group:hover .input-group-append:not(.btn) .input-group-text svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.2392156863);
}
body.dark .input-group-sm > .btn, body.dark .input-group-sm > .form-control, body.dark .input-group-sm > .form-select, body.dark .input-group-sm > .input-group-text {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
body.dark .invalid-feedback {
  color: #e7515a;
  font-size: 13px;
  letter-spacing: 1px;
}
body.dark .valid-feedback {
  color: #009688;
  font-size: 13px;
  letter-spacing: 1px;
}
body.dark .valid-tooltip {
  background-color: #009688;
}
body.dark .invalid-tooltip {
  background-color: #e7515a;
}
body.dark .custom-select.is-valid, body.dark .form-control.is-valid {
  border-color: #009688;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23009688' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-check'%3e%3cpolyline points='20 6 9 17 4 12'%3e%3c/polyline%3e%3c/svg%3e");
}
body.dark .was-validated .custom-select:valid, body.dark .was-validated .form-control:valid {
  border-color: #009688;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23009688' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-check'%3e%3cpolyline points='20 6 9 17 4 12'%3e%3c/polyline%3e%3c/svg%3e");
}
body.dark .custom-control-input.is-valid ~ .custom-control-label, body.dark .was-validated .custom-control-input:valid ~ .custom-control-label {
  color: #009688;
}
body.dark .form-control.is-invalid, body.dark .was-validated .form-control:invalid {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23e7515a' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-x'%3e%3cline x1='18' y1='6' x2='6' y2='18'%3e%3c/line%3e%3cline x1='6' y1='6' x2='18' y2='18'%3e%3c/line%3e%3c/svg%3e");
}
body.dark .custom-control-input.is-invalid ~ .custom-control-label, body.dark .was-validated .custom-control-input:invalid ~ .custom-control-label {
  color: #e7515a;
}
body.dark .dropdown-toggle:after, body.dark .dropup .dropdown-toggle::after, body.dark .dropend .dropdown-toggle::after, body.dark .dropstart .dropdown-toggle::before {
  display: none;
}
body.dark .dropdown-toggle svg.feather[class*=feather-chevron-] {
  width: 15px;
  height: 15px;
  vertical-align: middle;
}
body.dark .btn {
  padding: 0.4375rem 1.25rem;
  text-shadow: none;
  font-size: 14px;
  color: #3b3f5c;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  border-radius: 6px;
  cursor: pointer;
  background-color: #f1f2f3;
  box-shadow: 0px 5px 20px 0 rgba(0, 0, 0, 0.1);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
}
body.dark .btn svg {
  /* width: 20px;
  height: 20px;
  vertical-align: sub; */
  pointer-events: none;
  height: 22px;
  width: 22px;
  vertical-align: middle;
}
body.dark .btn .btn-text-inner {
  margin-left: 3px;
  vertical-align: middle;
  pointer-events: none;
}
body.dark .btn.btn-icon {
  padding: 7.5px 9px;
}
body.dark .btn.btn-icon.btn-rounded {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
body.dark .btn.rounded-circle {
  height: 40px;
  width: 40px;
  padding: 8px 8px;
}
body.dark .btn:hover {
  color: #3b3f5c;
  background-color: #f1f2f3;
  border-color: #d3d3d3;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
}
body.dark .btn-group .btn:hover, body.dark .btn-group .btn:focus {
  -webkit-transform: none;
  transform: none;
}
body.dark .btn.disabled, body.dark .btn.btn[disabled] {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn.disabled:hover, body.dark .btn.btn[disabled]:hover {
  cursor: not-allowed;
}
body.dark .btn .caret {
  border-top-color: #0e1726;
  margin-top: 0;
  margin-left: 3px;
  vertical-align: middle;
}
body.dark .btn + .caret, body.dark .btn + .dropdown-toggle .caret {
  margin-left: 0;
}
body.dark .btn-group > .btn, body.dark .btn-group .btn {
  padding: 8px 14px;
}
body.dark .btn-group-lg > .btn, body.dark .btn-group-lg .btn {
  font-size: 1.125rem;
}
body.dark .btn-group-lg > .btn {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}
body.dark .btn-lg {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}
body.dark .btn-group > .btn.btn-lg, body.dark .btn-group .btn.btn-lg {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}
body.dark .btn-group-lg > .btn, body.dark .btn-group-lg .btn {
  font-size: 1.125rem;
}
body.dark .btn-group-sm > .btn, body.dark .btn-sm {
  font-size: 0.6875rem;
}
body.dark .btn-group > .btn.btn-sm, body.dark .btn-group .btn.btn-sm {
  font-size: 0.6875rem;
}
body.dark .btn-group .dropdown-menu {
  border: none;
  z-index: 1028;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  /* top: 0!important; */
  right: auto;
  border-radius: 8px;
  background-color: #1b2e4b;
}
body.dark .btn-group .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #bfc9d4;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}
body.dark .dropdown-divider {
  border-top: 1px solid #0e1726;
}
body.dark .btn-group .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
}
body.dark .btn-group .dropdown-menu a.dropdown-item svg {
  cursor: pointer;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: rgba(0, 23, 55, 0.08);
}
body.dark .btn-group .dropdown-menu a.dropdown-item:hover svg {
  color: #4361ee;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  border: none;
  z-index: 899;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  transition: top 0.3s ease-in-out 0s, opacity 0.3s ease-in-out 0s, visibility 0.3s ease-in-out 0s;
  opacity: 0;
  visibility: hidden;
  display: block !important;
  transform: none !important;
  top: 0 !important;
  border-radius: 8px;
  background: #1b2e4b;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.right {
  right: auto;
  left: auto !important;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.left {
  inset: 0 0 auto auto !important;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  top: 21px !important;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  display: block;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #bfc9d4;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item svg {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: bottom;
  color: #888ea8;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:hover svg {
  color: #2196f3;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item.active, body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:active {
  background-color: transparent;
  color: #22c7d5;
  font-weight: 700;
}
body.dark .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
}
body.dark .btn-primary:not(:disabled):not(.disabled).active:focus, body.dark .btn-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-primary.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-secondary:not(:disabled):not(.disabled).active:focus, body.dark .btn-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-success:not(:disabled):not(.disabled).active:focus, body.dark .btn-success:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-success.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-info:not(:disabled):not(.disabled).active:focus, body.dark .btn-info:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-info.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-danger:not(:disabled):not(.disabled).active:focus, body.dark .btn-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-danger.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-warning:not(:disabled):not(.disabled).active:focus, body.dark .btn-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-warning.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-secondary:not(:disabled):not(.disabled).active:focus, body.dark .btn-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-dark:not(:disabled):not(.disabled).active:focus, body.dark .btn-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-dark.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-primary:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-success:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-success:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-info:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-info:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-danger:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-warning:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn-outline-dark:not(:disabled):not(.disabled).active:focus, body.dark .btn-outline-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
body.dark .show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: none;
}
body.dark .btn.focus, body.dark .btn:focus {
  box-shadow: none;
}
body.dark .btn-success:focus, body.dark .btn-info:focus, body.dark .btn-danger:focus, body.dark .btn-warning:focus, body.dark .btn-secondary:focus, body.dark .btn-dark:focus, body.dark .btn-outline-success:focus, body.dark .btn-outline-info:focus, body.dark .btn-outline-danger:focus, body.dark .btn-outline-warning:focus, body.dark .btn-outline-secondary:focus, body.dark .btn-outline-dark:focus body.dark .btn-light-default:focus, body.dark .btn-light-primary:focus, body.dark .btn-light-success:focus, body.dark .btn-light-info:focus, body.dark .btn-light-danger:focus, body.dark .btn-light-warning:focus, body.dark .btn-light-secondary:focus, body.dark .btn-light-dark:focus {
  box-shadow: none;
}
body.dark .btn-primary {
  color: #fff !important;
  background-color: #4361ee !important;
  border-color: #4361ee;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59);
}
body.dark .btn-primary:hover, body.dark .btn-primary:focus {
  color: #fff !important;
  background-color: #4361ee !important;
  box-shadow: none;
  border-color: #4361ee !important;
}
body.dark .btn-primary:active, body.dark .btn-primary.active {
  background-color: #4361ee;
  border-top: 1px solid #4361ee;
}
body.dark .btn-primary.disabled, body.dark .btn-primary.btn[disabled], body.dark .btn-primary:disabled {
  background-color: #4361ee;
  border-color: #4361ee;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-primary.active.focus, body.dark .btn-primary.active:focus, body.dark .btn-primary.active:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
body.dark .btn-primary.focus:active {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
body.dark .btn-primary:active:focus, body.dark .btn-primary:active:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
body.dark.open > .dropdown-toggle.btn-primary.focus, body.dark.open > .dropdown-toggle.btn-primary:focus, body.dark.open > .dropdown-toggle.btn-primary:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
body.dark .btn-primary:not(:disabled):not(.disabled).active, body.dark .btn-primary:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .show > .btn-primary.dropdown-toggle {
  color: #fff !important;
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .btn-primary .caret {
  border-top-color: #fff;
}
body.dark .btn-group.open .btn-primary.dropdown-toggle {
  background-color: #bfc1fb;
}
body.dark .btn-secondary {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
  box-shadow: 0 10px 20px -10px rgba(92, 26, 195, 0.59);
}
body.dark .btn-secondary:hover, body.dark .btn-secondary:focus {
  color: #fff !important;
  background-color: #805dca !important;
  box-shadow: none;
  border-color: #805dca !important;
}
body.dark .btn-secondary:active, body.dark .btn-secondary.active {
  background-color: #805dca;
  border-top: 1px solid #805dca;
}
body.dark .btn-secondary:not(:disabled):not(.disabled).active, body.dark .btn-secondary:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
}
body.dark .show > .btn-secondary.dropdown-toggle {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
}
body.dark .btn-secondary.disabled, body.dark .btn-secondary.btn[disabled], body.dark .btn-secondary:disabled {
  background-color: #805dca;
  border-color: #805dca;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-secondary .caret {
  border-top-color: #fff;
}
body.dark .btn-info {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
  box-shadow: 0 10px 20px -10px rgba(33, 150, 243, 0.59);
}
body.dark .btn-info:hover, body.dark .btn-info:focus {
  color: #fff !important;
  background-color: #2196f3 !important;
  box-shadow: none;
  border-color: #2196f3 !important;
}
body.dark .btn-info:active, body.dark .btn-info.active {
  background-color: #2196f3;
  border-top: 1px solid #2196f3;
}
body.dark .btn-info:not(:disabled):not(.disabled).active, body.dark .btn-info:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .show > .btn-info.dropdown-toggle {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .btn-info.disabled, body.dark .btn-info.btn[disabled], body.dark .btn-info:disabled {
  background-color: #2196f3;
  border-color: #2196f3;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn.disabled, body.dark .btn:disabled {
  opacity: 0.35;
}
body.dark fieldset:disabled .btn {
  opacity: 0.35;
}
body.dark .btn-info.active.focus, body.dark .btn-info.active:focus, body.dark .btn-info.active:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .btn-info.focus:active {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .btn-info:active:focus, body.dark .btn-info:active:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .open > .dropdown-toggle.btn-info.focus, body.dark .open > body.dark .dropdown-toggle.btn-info:focus, body.dark .open > .dropdown-toggle.btn-info:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .btn-info .caret {
  border-top-color: #fff;
}
body.dark .btn-group.open .btn-info.dropdown-toggle {
  background-color: #a6d5fa;
}
body.dark .btn-warning {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
  box-shadow: 0 10px 20px -10px rgba(226, 160, 63, 0.59);
}
body.dark .btn-warning:hover, body.dark .btn-warning:focus {
  color: #fff !important;
  background-color: #e2a03f !important;
  box-shadow: none;
  border-color: #e2a03f !important;
}
body.dark .btn-warning:active, body.dark .btn-warning.active {
  background-color: #e2a03f;
  border-top: 1px solid #e2a03f;
}
body.dark .btn-warning:not(:disabled):not(.disabled).active, body.dark .btn-warning:not(:disabled):not(.disabled):active {
  color: #0e1726;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .show > .btn-warning.dropdown-toggle {
  color: #0e1726;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .btn-warning.disabled, body.dark .btn-warning.btn[disabled], body.dark .btn-warning:disabled {
  background-color: #e2a03f;
  border-color: #e2a03f;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-warning.active.focus, body.dark .btn-warning.active:focus, body.dark .btn-warning.active:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .btn-warning.focus:active {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .btn-warning:active:focus, body.dark .btn-warning:active:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .open > .dropdown-toggle.btn-warning.focus, body.dark .open > .dropdown-toggle.btn-warning:focus {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

/*      Form Control File       */
/*      Form Control Custom File       */
/*      Input Group      */
/*      Input Group append       */
/*      Input Group Append       */
/*      Validation Customization      */
/*      Default Buttons       */
body.dark {
  /* Light Buttons  */
  /*  
      ====================
          Table
      ====================
  */
  /*

      Hover

  */
  /* 
      Hover and Striped
  */
  /* 

      Striped

  */
  /* 
      Striped and Bordered
  */
}
body.dark .open > .dropdown-toggle.btn-warning:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .btn-warning .caret {
  border-top-color: #fff;
}
body.dark .btn-group.open .btn-warning.dropdown-toggle {
  background-color: #df8505;
}
body.dark .btn-danger {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
  box-shadow: 0 10px 20px -10px rgba(231, 81, 90, 0.59);
}
body.dark .btn-danger:hover, body.dark .btn-danger:focus {
  color: #fff !important;
  background-color: #e7515a !important;
  box-shadow: none;
  border-color: #e7515a !important;
}
body.dark .btn-danger:active, body.dark .btn-danger.active {
  background-color: #e7515a;
  border-top: 1px solid #e7515a;
}
body.dark .btn-danger:not(:disabled):not(.disabled).active, body.dark .btn-danger:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .show > .btn-danger.dropdown-toggle {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .btn-danger.disabled, body.dark .btn-danger.btn[disabled], body.dark .btn-danger:disabled {
  background-color: #e7515a;
  border-color: #e7515a;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-danger.active.focus, body.dark .btn-danger.active:focus, body.dark .btn-danger.active:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
body.dark .btn-danger.focus:active {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
body.dark .btn-danger:active:focus, body.dark .btn-danger:active:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
body.dark .open > .dropdown-toggle.btn-danger.focus, body.dark .open > .dropdown-toggle.btn-danger:focus, body.dark .open > .dropdown-toggle.btn-danger:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
body.dark .btn-danger .caret {
  border-top-color: #fff;
}
body.dark .btn-group.open .btn-danger.dropdown-toggle {
  background-color: #a9302a;
}
body.dark .btn-dark {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
  box-shadow: 0 10px 20px -10px rgba(59, 63, 92, 0.59);
}
body.dark .btn-dark:hover, body.dark .btn-dark:focus {
  color: #fff !important;
  background-color: #3b3f5c !important;
  box-shadow: none;
  border-color: #3b3f5c !important;
}
body.dark .btn-dark:active, body.dark .btn-dark.active {
  background-color: #3b3f5c;
  border-top: 1px solid #3b3f5c;
}
body.dark .btn-dark:not(:disabled):not(.disabled).active, body.dark .btn-dark:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}
body.dark .show > .btn-dark.dropdown-toggle {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}
body.dark .btn-dark.disabled, body.dark .btn-dark.btn[disabled], body.dark .btn-dark:disabled {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-dark .caret {
  border-top-color: #fff;
}
body.dark .btn-group.open .btn-dark.dropdown-toggle {
  background-color: #484848;
}
body.dark .btn-success {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
  box-shadow: 0 10px 20px -10px rgba(0, 171, 85, 0.59);
}
body.dark .btn-success:hover, body.dark .btn-success:focus {
  color: #fff !important;
  background-color: #00ab55 !important;
  box-shadow: none;
  border-color: #00ab55 !important;
}
body.dark .btn-success:active, body.dark .btn-success.active {
  background-color: #00ab55;
  border-top: 1px solid #00ab55;
}
body.dark .btn-success:not(:disabled):not(.disabled).active, body.dark .btn-success:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .show > .btn-success.dropdown-toggle {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .btn-success.disabled, body.dark .btn-success.btn[disabled], body.dark .btn-success:disabled {
  background-color: #00ab55;
  border-color: #00ab55;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-success.active.focus, body.dark .btn-success.active:focus, body.dark .btn-success.active:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
body.dark .btn-success.focus:active {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
body.dark .btn-success:active:focus, body.dark .btn-success:active:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
body.dark .open > .dropdown-toggle.btn-success.focus, body.dark .open > .dropdown-toggle.btn-success:focus, body.dark .open > .dropdown-toggle.btn-success:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
body.dark .btn-success .caret {
  border-top-color: #fff;
}
body.dark .btn.box-shadow-none {
  border: none;
}
body.dark .btn.box-shadow-none:hover, body.dark .btn.box-shadow-none:focus {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}
body.dark .box-shadow-none {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
}
body.dark .btn.box-shadow-none:not(:disabled):not(.disabled).active, body.dark .btn.box-shadow-none:not(:disabled):not(.disabled):active {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}
body.dark .show > .btn.box-shadow-none.dropdown-toggle {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}
body.dark .btn-group.open .btn-success.dropdown-toggle {
  background-color: #499249;
}
body.dark .btn-dismiss {
  color: #0e1726;
  background-color: #fff !important;
  border-color: #fff;
  padding: 3px 7px;
}
body.dark .btn-dismiss:hover, body.dark .btn-dismiss:focus {
  color: #0e1726;
  background-color: #fff;
}
body.dark .btn-dismiss:active, body.dark .btn-dismiss.active {
  background-color: #fff;
  border-top: 1px solid #fff;
}
body.dark .btn-group > .btn i {
  margin-right: 3px;
}
body.dark .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
body.dark .btn-group > .btn + .dropdown-toggle {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
body.dark .btn-group-vertical > .btn-check:checked + .btn, body.dark .btn-group-vertical > .btn-check:focus + .btn {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
body.dark .btn-group-vertical > .btn.active, body.dark .btn-group-vertical > .btn:active, body.dark .btn-group-vertical > .btn:focus, body.dark .btn-group-vertical > .btn:hover {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
body.dark .btn-group > .btn-check:checked + .btn, body.dark .btn-group > .btn-check:focus + .btn {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
body.dark .btn-group > .btn.active, body.dark .btn-group > .btn:active, body.dark .btn-group > .btn:focus, body.dark .btn-group > .btn:hover {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
body.dark .btn-group-vertical > .btn:active {
  box-shadow: none;
}
body.dark .btn-group > .btn:hover {
  opacity: 0.8;
}
body.dark .btn-group-vertical > .btn-group:not(:first-child) {
  margin-bottom: 0;
}
body.dark .btn-group-vertical > .btn:not(:first-child) {
  margin-bottom: 0;
}
body.dark .btn-group-vertical > .btn:hover {
  opacity: 0.8;
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-primary {
  border-left: 1px solid rgb(93, 119, 243);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-success {
  border-left: 1px solid rgb(74, 203, 138);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-info {
  border-left: 1px solid rgb(73, 172, 251);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-warning {
  border-left: 1px solid rgb(245, 180, 85);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-danger {
  border-left: 1px solid rgb(241, 132, 139);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-dark {
  border-left: 1px solid rgb(74, 78, 106);
}
body.dark .btn-group > .btn + .dropdown-toggle.btn-secondary {
  border-left: 1px solid rgb(149, 112, 227);
}
body.dark .btn-group.dropstart .dropdown-toggle-split {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
body.dark .btn-group.dropstart .btn-primary:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(68, 104, 253);
}
body.dark .btn-group.dropstart .btn-success:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(163, 198, 111);
}
body.dark .btn-group.dropstart .btn-info:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(73, 172, 251);
}
body.dark .btn-group.dropstart .btn-warning:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(245, 180, 85);
}
body.dark .btn-group.dropstart .btn-danger:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(241, 132, 139);
}
body.dark .btn-group.dropstart .btn-dark:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(112, 118, 122);
}
body.dark .btn-group.dropstart .btn-secondary:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(131, 83, 220);
}
body.dark .btn .badge.badge-align-right {
  position: absolute;
  top: -1px;
  right: 8px;
}
body.dark .dropup .btn .caret {
  border-bottom-color: #0e1726;
}
body.dark .btn-outline-primary:not(:disabled):not(.disabled).active, body.dark .btn-outline-primary:not(:disabled):not(.disabled):active {
  background-color: #4361ee;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-primary.dropdown-toggle.show:focus {
  background-color: #4361ee;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-success:not(:disabled):not(.disabled).active, body.dark .btn-outline-success:not(:disabled):not(.disabled):active {
  background-color: #00ab55;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-success.dropdown-toggle.show:focus {
  background-color: #00ab55;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-info:not(:disabled):not(.disabled).active, body.dark .btn-outline-info:not(:disabled):not(.disabled):active {
  background-color: #2196f3;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-info.dropdown-toggle.show:focus {
  background-color: #2196f3;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-danger:not(:disabled):not(.disabled).active, body.dark .btn-outline-danger:not(:disabled):not(.disabled):active {
  background-color: #e7515a;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-danger.dropdown-toggle.show:focus {
  background-color: #e7515a;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-warning:not(:disabled):not(.disabled).active, body.dark .btn-outline-warning:not(:disabled):not(.disabled):active {
  background-color: #e2a03f;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-warning.dropdown-toggle.show:focus {
  background-color: #e2a03f;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-secondary:not(:disabled):not(.disabled).active, body.dark .btn-outline-secondary:not(:disabled):not(.disabled):active {
  background-color: #805dca;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-secondary.dropdown-toggle.show:focus {
  background-color: #805dca;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-dark:not(:disabled):not(.disabled).active, body.dark .btn-outline-dark:not(:disabled):not(.disabled):active {
  background-color: #3b3f5c;
  color: #fff !important;
  box-shadow: none;
}
body.dark .btn-outline-dark.dropdown-toggle.show:focus {
  background-color: #3b3f5c;
  color: #fff !important;
  box-shadow: none;
}
body.dark .show > .btn-outline-primary.dropdown-toggle:after, body.dark .show > .btn-outline-success.dropdown-toggle:after, body.dark .show > .btn-outline-info.dropdown-toggle:after, body.dark .show > .btn-outline-danger.dropdown-toggle:after, body.dark .show > .btn-outline-warning.dropdown-toggle:after, body.dark .show > .btn-outline-secondary.dropdown-toggle:after, body.dark .show > .btn-outline-dark.dropdown-toggle:after, body.dark .show > .btn-outline-primary.dropdown-toggle:before, body.dark .show > .btn-outline-success.dropdown-toggle:before, body.dark .show > .btn-outline-info.dropdown-toggle:before, body.dark .show > .btn-outline-danger.dropdown-toggle:before, body.dark .show > .btn-outline-warning.dropdown-toggle:before, body.dark .show > .btn-outline-secondary.dropdown-toggle:before, body.dark .show > .btn-outline-dark.dropdown-toggle:before {
  color: #fff !important;
}
body.dark .btn-outline-primary {
  border: 1px solid #4361ee !important;
  color: #4361ee !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-info {
  border: 1px solid #2196f3 !important;
  color: #2196f3 !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-warning {
  border: 1px solid #e2a03f !important;
  color: #e2a03f !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-success {
  border: 1px solid #00ab55 !important;
  color: #00ab55 !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-danger {
  border: 1px solid #e7515a !important;
  color: #e7515a !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-secondary {
  border: 1px solid #805dca !important;
  color: #805dca !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-dark {
  border: 1px solid #3b3f5c !important;
  color: #bfc9d4 !important;
  background-color: transparent;
  box-shadow: none;
}
body.dark .btn-outline-dark.disabled, body.dark .btn-outline-dark:disabled {
  color: #bfc9d4 !important;
}
body.dark .btn-outline-primary:hover, body.dark .btn-outline-info:hover, body.dark .btn-outline-warning:hover, body.dark .btn-outline-success:hover, body.dark .btn-outline-danger:hover, body.dark .btn-outline-secondary:hover, body.dark .btn-outline-dark:hover {
  box-shadow: 0px 5px 20px 0 rgba(0, 0, 0, 0.1);
}
body.dark .btn-outline-primary:hover {
  color: #fff !important;
  background-color: #4361ee !important;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59) !important;
}
body.dark .btn-outline-info:hover {
  color: #fff !important;
  background-color: #2196f3 !important;
  box-shadow: 0 10px 20px -10px rgba(33, 150, 243, 0.588) !important;
}
body.dark .btn-outline-warning:hover {
  color: #fff !important;
  background-color: #e2a03f !important;
  box-shadow: 0 10px 20px -10px rgba(226, 160, 63, 0.588) !important;
}
body.dark .btn-outline-success:hover {
  color: #fff !important;
  background-color: #00ab55 !important;
  box-shadow: 0 10px 20px -10px rgba(0, 171, 85, 0.59) !important;
}
body.dark .btn-outline-danger:hover {
  color: #fff !important;
  background-color: #e7515a !important;
  box-shadow: 0 10px 20px -10px rgba(231, 81, 90, 0.588) !important;
}
body.dark .btn-outline-secondary:hover {
  color: #fff !important;
  background-color: #805dca !important;
  box-shadow: 0 10px 20px -10px rgba(92, 26, 195, 0.59) !important;
}
body.dark .btn-outline-dark:hover {
  color: #fff !important;
  background-color: #3b3f5c !important;
  box-shadow: 0 10px 20px -10px rgba(59, 63, 92, 0.59) !important;
}
body.dark .btn-check:active + .btn-outline-primary, body.dark .btn-check:checked + .btn-outline-primary {
  background-color: #4361ee !important;
  color: #fff !important;
}
body.dark .btn-outline-primary.active, body.dark .btn-outline-primary.dropdown-toggle.show, body.dark .btn-outline-primary:active {
  background-color: #4361ee !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-info, body.dark .btn-check:checked + .btn-outline-info {
  background-color: #2196f3 !important;
  color: #fff !important;
}
body.dark .btn-outline-info.active, body.dark .btn-outline-info.dropdown-toggle.show, body.dark .btn-outline-info:active {
  background-color: #2196f3 !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-success, body.dark .btn-check:checked + .btn-outline-success {
  background-color: #00ab55 !important;
  color: #fff !important;
}
body.dark .btn-outline-success.active, body.dark .btn-outline-success.dropdown-toggle.show, body.dark .btn-outline-success:active {
  background-color: #00ab55 !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-warning, body.dark .btn-check:checked + .btn-outline-warning {
  background-color: #e2a03f !important;
  color: #fff !important;
}
body.dark .btn-outline-warning.active, body.dark .btn-outline-warning.dropdown-toggle.show, body.dark .btn-outline-warning:active {
  background-color: #e2a03f !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-danger, body.dark .btn-check:checked + .btn-outline-danger {
  background-color: #e7515a !important;
  color: #fff !important;
}
body.dark .btn-outline-danger.active, body.dark .btn-outline-danger.dropdown-toggle.show, body.dark .btn-outline-danger:active {
  background-color: #e7515a !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-secondary, body.dark .btn-check:checked + .btn-outline-secondary {
  background-color: #805dca !important;
  color: #fff !important;
}
body.dark .btn-outline-secondary.active, body.dark .btn-outline-secondary.dropdown-toggle.show, body.dark .btn-outline-secondary:active {
  background-color: #805dca !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-dark, body.dark .btn-check:checked + .btn-outline-dark {
  background-color: #3b3f5c !important;
  color: #fff !important;
}
body.dark .btn-outline-dark.active, body.dark .btn-outline-dark.dropdown-toggle.show, body.dark .btn-outline-dark:active {
  background-color: #3b3f5c !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-outline-primary:focus, body.dark .btn-check:checked + .btn-outline-primary:focus {
  box-shadow: none;
}
body.dark .btn-outline-primary.active:focus, body.dark .btn-outline-primary.dropdown-toggle.show:focus, body.dark .btn-outline-primary:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-primary, body.dark .btn-outline-primary:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-info:focus, body.dark .btn-check:checked + .btn-outline-info:focus {
  box-shadow: none;
}
body.dark .btn-outline-info.active:focus, body.dark .btn-outline-info.dropdown-toggle.show:focus, body.dark .btn-outline-info:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-info, body.dark .btn-outline-info:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-success:focus, body.dark .btn-check:checked + .btn-outline-success:focus {
  box-shadow: none;
}
body.dark .btn-outline-success.active:focus, body.dark .btn-outline-success.dropdown-toggle.show:focus, body.dark .btn-outline-success:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-success, body.dark .btn-outline-success:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-danger:focus, body.dark .btn-check:checked + .btn-outline-danger:focus {
  box-shadow: none;
}
body.dark .btn-outline-danger.active:focus, body.dark .btn-outline-danger.dropdown-toggle.show:focus, body.dark .btn-outline-danger:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-danger, body.dark .btn-outline-danger:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-secondary:focus, body.dark .btn-check:checked + .btn-outline-secondary:focus {
  box-shadow: none;
}
body.dark .btn-outline-secondary.active:focus, body.dark .btn-outline-secondary.dropdown-toggle.show:focus, body.dark .btn-outline-secondary:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-secondary, body.dark .btn-outline-secondary:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-warning:focus, body.dark .btn-check:checked + .btn-outline-warning:focus {
  box-shadow: none;
}
body.dark .btn-outline-warning.active:focus, body.dark .btn-outline-warning.dropdown-toggle.show:focus, body.dark .btn-outline-warning:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-warning, body.dark .btn-outline-warning:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-outline-dark:focus, body.dark .btn-check:checked + .btn-outline-dark:focus {
  box-shadow: none;
}
body.dark .btn-outline-dark.active:focus, body.dark .btn-outline-dark.dropdown-toggle.show:focus, body.dark .btn-outline-dark:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-outline-dark, body.dark .btn-outline-dark:focus {
  box-shadow: none;
}
body.dark [class*=btn-light-] {
  box-shadow: none;
}
body.dark .btn-light-primary {
  color: #778ef7;
  background-color: #152143;
  border: 1px solid #152143;
}
body.dark .btn-light-primary:hover {
  background-color: #152143 !important;
  border: 1px solid #152143 !important;
  color: #778ef7 !important;
}
body.dark .btn-light-info {
  color: #4db0ff;
  background-color: #0b2f52;
  border: 1px solid #0b2f52;
}
body.dark .btn-light-info:hover {
  background-color: #0b2f52 !important;
  border: 1px solid #0b2f52 !important;
  color: #4db0ff !important;
}
body.dark .btn-light-warning {
  color: #eab764;
  background-color: #282625;
  border: 1px solid #282625;
}
body.dark .btn-light-warning:hover {
  background-color: #282625 !important;
  border: 1px solid #282625 !important;
  color: #eab764 !important;
}
body.dark .btn-light-success {
  color: #4dc187;
  background-color: #0c272b;
  border: 1px solid #0c272b;
}
body.dark .btn-light-success:hover {
  background-color: #0c272b !important;
  border: 1px solid #0c272b !important;
  color: #4dc187 !important;
}
body.dark .btn-light-danger {
  color: #e67980;
  background-color: #2c1c2b;
  border: 1px solid #2c1c2b;
}
body.dark .btn-light-danger:hover {
  background-color: #2c1c2b !important;
  border: 1px solid #2c1c2b !important;
  color: #e67980 !important;
}
body.dark .btn-light-secondary {
  color: #a46edb;
  background-color: #1d1a3b;
  border: 1px solid #1d1a3b;
}
body.dark .btn-light-secondary:hover {
  background-color: #1d1a3b !important;
  border: 1px solid #1d1a3b !important;
  color: #a46edb !important;
}
body.dark .btn-light-dark {
  color: #abacb2;
  background-color: #181e2e;
  border: 1px solid #181e2e;
}
body.dark .btn-light-dark:hover {
  background-color: #181e2e !important;
  border: 1px solid #181e2e !important;
  color: #abacb2 !important;
}
body.dark .btn-check:active + .btn-light-primary, body.dark .btn-check:checked + .btn-light-primary {
  background-color: #4361ee !important;
  color: #fff !important;
}
body.dark .btn-light-primary.dropdown-toggle.show {
  background-color: #4361ee !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-info, body.dark .btn-check:checked + .btn-light-info {
  background-color: #2196f3 !important;
  color: #fff !important;
}
body.dark .btn-light-info.dropdown-toggle.show {
  background-color: #2196f3 !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-success, body.dark .btn-check:checked + .btn-light-success {
  background-color: #00ab55 !important;
  color: #fff !important;
}
body.dark .btn-light-success.dropdown-toggle.show {
  background-color: #00ab55 !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-warning, body.dark .btn-check:checked + .btn-light-warning {
  background-color: #e2a03f !important;
  color: #fff !important;
}
body.dark .btn-light-warning.dropdown-toggle.show {
  background-color: #e2a03f !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-danger, body.dark .btn-check:checked + .btn-light-danger {
  background-color: #e7515a !important;
  color: #fff !important;
}
body.dark .btn-light-danger.dropdown-toggle.show {
  background-color: #e7515a !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-secondary, body.dark .btn-check:checked + .btn-light-secondary {
  background-color: #805dca !important;
  color: #fff !important;
}
body.dark .btn-light-secondary.dropdown-toggle.show {
  background-color: #805dca !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-dark, body.dark .btn-check:checked + .btn-light-dark {
  background-color: #3b3f5c !important;
  color: #fff !important;
}
body.dark .btn-light-dark.dropdown-toggle.show {
  background-color: #3b3f5c !important;
  color: #fff !important;
}
body.dark .btn-check:active + .btn-light-primary:focus, body.dark .btn-check:checked + .btn-light-primary:focus {
  box-shadow: none;
}
body.dark .btn-light-primary.active:focus, body.dark .btn-light-primary.dropdown-toggle.show:focus, body.dark .btn-light-primary:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-primary, body.dark .btn-light-primary:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-info:focus, body.dark .btn-check:checked + .btn-light-info:focus {
  box-shadow: none;
}
body.dark .btn-light-info.active:focus, body.dark .btn-light-info.dropdown-toggle.show:focus, body.dark .btn-light-info:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-info, body.dark .btn-light-info:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-success:focus, body.dark .btn-check:checked + .btn-light-success:focus {
  box-shadow: none;
}
body.dark .btn-light-success.active:focus, body.dark .btn-light-success.dropdown-toggle.show:focus, body.dark .btn-light-success:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-success, body.dark .btn-light-success:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-danger:focus, body.dark .btn-check:checked + .btn-light-danger:focus {
  box-shadow: none;
}
body.dark .btn-light-danger.active:focus, body.dark .btn-light-danger.dropdown-toggle.show:focus, body.dark .btn-light-danger:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-danger, body.dark .btn-light-danger:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-secondary:focus, body.dark .btn-check:checked + .btn-light-secondary:focus {
  box-shadow: none;
}
body.dark .btn-light-secondary.active:focus, body.dark .btn-light-secondary.dropdown-toggle.show:focus, body.dark .btn-light-secondary:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-secondary, body.dark .btn-light-secondary:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-warning:focus, body.dark .btn-check:checked + .btn-light-warning:focus {
  box-shadow: none;
}
body.dark .btn-light-warning.active:focus, body.dark .btn-light-warning.dropdown-toggle.show:focus, body.dark .btn-light-warning:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-warning, body.dark .btn-light-warning:focus {
  box-shadow: none;
}
body.dark .btn-check:active + .btn-light-dark:focus, body.dark .btn-check:checked + .btn-light-dark:focus {
  box-shadow: none;
}
body.dark .btn-light-dark.active:focus, body.dark .btn-light-dark.dropdown-toggle.show:focus, body.dark .btn-light-dark:active:focus {
  box-shadow: none;
}
body.dark .btn-check:focus + .btn-light-dark, body.dark .btn-light-dark:focus {
  box-shadow: none;
}
body.dark .btn-rounded {
  -webkit-border-radius: 1.875rem;
  -moz-border-radius: 1.875rem;
  -ms-border-radius: 1.875rem;
  -o-border-radius: 1.875rem;
  border-radius: 1.875rem;
}
body.dark .form-check.form-check-primary .form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .form-check.form-check-success .form-check-input:checked {
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .form-check.form-check-danger .form-check-input:checked {
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .form-check.form-check-secondary .form-check-input:checked {
  background-color: #805dca;
  border-color: #805dca;
}
body.dark .form-check.form-check-warning .form-check-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .form-check.form-check-info .form-check-input:checked {
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .form-check.form-check-dark .form-check-input:checked {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}
body.dark .form-switch .form-check-input {
  /* width: 2em; */
  width: 35px;
  height: 18px;
}
body.dark .form-switch .form-check-input:focus {
  border-color: transparent;
}
body.dark .form-switch .form-check-input:not(:checked):focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
}
body.dark .form-switch .form-check-label {
  margin-left: 8px;
  vertical-align: text-top;
}
body.dark .form-switch.form-switch-primary .form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
body.dark .form-switch.form-switch-success .form-check-input:checked {
  background-color: #00ab55;
  border-color: #00ab55;
}
body.dark .form-switch.form-switch-danger .form-check-input:checked {
  background-color: #e7515a;
  border-color: #e7515a;
}
body.dark .form-switch.form-switch-secondary .form-check-input:checked {
  background-color: #805dca;
  border-color: #805dca;
}
body.dark .form-switch.form-switch-warning .form-check-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}
body.dark .form-switch.form-switch-info .form-check-input:checked {
  background-color: #2196f3;
  border-color: #2196f3;
}
body.dark .form-switch.form-switch-dark .form-check-input:checked {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}
body.dark .data-marker {
  padding: 2px;
  border-radius: 50%;
  font-size: 18px;
  display: inline-flex;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
body.dark .data-marker-success {
  background-color: #00ab55;
}
body.dark .data-marker-warning {
  background-color: #e2a03f;
}
body.dark .data-marker-danger, body.dark .data-marker-info, body.dark .data-marker-dark {
  background-color: #e7515a;
}
body.dark .badge {
  font-weight: 600;
  line-height: 1.4;
  font-size: 11.9px;
  font-weight: 600;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  display: inline-block;
  padding: 4.6px 8px;
  color: #FFF;
  border-radius: 6px;
}
body.dark .badge:hover {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
}
body.dark .badge:empty {
  display: none;
}
body.dark .badge--group {
  display: inline-flex;
}
body.dark .badge--group .badge {
  border: 2px solid #191e3a;
}
body.dark .badge--group .badge:not(:first-child) {
  margin-left: -6px;
}
body.dark .badge-dot:empty {
  display: block;
}
body.dark .badge--group .badge-dot {
  /* width: 15px; */
  /* height: 15px; */
  /* border-radius: 50%; */
  /* padding: 7px; */
  width: 16px;
  height: 16px;
  border-radius: 50%;
  padding: 0;
}
body.dark .badge svg {
  width: 15px;
  height: 15px;
  vertical-align: top;
  margin-right: 3px;
}
body.dark .badge.badge-enabled {
  background-color: #00ab55;
  color: #fff;
}
body.dark .badge.badge-disable {
  background-color: #e7515a;
  color: #fff;
}
body.dark .badge-collapsed-img img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid #515365;
  margin-left: -21px;
}
body.dark .badge-collapsed-img.badge-tooltip img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid #ffffff;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.3);
  margin-left: -21px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .badge-collapsed-img.badge-tooltip img:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
body.dark .badge-collapsed-img.translateY-axis img {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .badge-collapsed-img.translateY-axis img:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
body.dark .badge-collapsed-img.rectangle-collapsed img {
  width: 45px;
  height: 32px;
}
body.dark .badge-collapsed-img.translateX-axis img {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .badge-collapsed-img.translateX-axis img:hover {
  -webkit-transform: translateX(5px) scale(1.02);
  transform: translateX(5px) scale(1.02);
}
body.dark .badge-primary {
  color: #fff;
  background-color: #4361ee;
}
body.dark .badge-info {
  color: #fff;
  background-color: #2196f3;
}
body.dark .badge-success {
  color: #fff;
  background-color: #00ab55;
}
body.dark .badge-danger {
  color: #fff;
  background-color: #e7515a;
}
body.dark .badge-warning {
  color: #fff;
  background-color: #e2a03f;
}
body.dark .badge-dark {
  color: #fff;
  background-color: #3b3f5c;
}
body.dark .badge-secondary {
  background-color: #805dca;
}
body.dark .outline-badge-primary {
  color: #4361ee;
  background-color: transparent;
  border: 1px solid #4361ee;
}
body.dark .outline-badge-info {
  color: #2196f3;
  background-color: transparent;
  border: 1px solid #2196f3;
}
body.dark .outline-badge-success {
  color: #00ab55;
  background-color: transparent;
  border: 1px solid #00ab55;
}
body.dark .outline-badge-danger {
  color: #e7515a;
  background-color: transparent;
  border: 1px solid #e7515a;
}
body.dark .outline-badge-warning {
  color: #e2a03f;
  background-color: transparent;
  border: 1px solid #e2a03f;
}
body.dark .outline-badge-dark {
  color: #bfc9d4;
  background-color: transparent;
  border: 1px solid #3b3f5c;
}
body.dark .outline-badge-secondary {
  color: #805dca;
  background-color: transparent;
  border: 1px solid #805dca;
}
body.dark .outline-badge-primary:focus, body.dark .outline-badge-primary:hover {
  background-color: #4361ee;
  color: #fff;
}
body.dark .outline-badge-secondary:focus, body.dark .outline-badge-secondary:hover {
  color: #fff;
  background-color: #805dca;
}
body.dark .outline-badge-success:focus, body.dark .outline-badge-success:hover {
  color: #fff;
  background-color: #00ab55;
}
body.dark .outline-badge-danger:focus, body.dark .outline-badge-danger:hover {
  color: #fff;
  background-color: #e7515a;
}
body.dark .outline-badge-warning:focus, body.dark .outline-badge-warning:hover {
  color: #fff;
  background-color: #e2a03f;
}
body.dark .outline-badge-info:focus, body.dark .outline-badge-info:hover {
  color: #fff;
  background-color: #2196f3;
}
body.dark .outline-badge-dark:focus, body.dark .outline-badge-dark:hover {
  color: #fff;
  background-color: #3b3f5c;
}
body.dark .badge-light-primary {
  color: #778ef7;
  background-color: #152143;
  border: 1px solid #152143;
}
body.dark .badge-light-info {
  color: #4db0ff;
  background-color: #0b2f52;
  border: 1px solid #0b2f52;
}
body.dark .badge-light-success {
  color: #4dc187;
  background-color: #0c272b;
  border: 1px solid #0c272b;
}
body.dark .badge-light-danger {
  color: #e67980;
  background-color: #2c1c2b;
  border: 1px solid #2c1c2b;
}
body.dark .badge-light-warning {
  color: #eab764;
  background-color: #282625;
  border: 1px solid #282625;
}
body.dark .badge-light-dark {
  color: #abacb2;
  background-color: #181e2e;
  border: 1px solid #181e2e;
}
body.dark .badge-light-secondary {
  color: #a46edb;
  background-color: #1d1a3b;
  border: 1px solid #1d1a3b;
}
body.dark .badge[class*=link-badge-] {
  cursor: pointer;
}
body.dark .link-badge-primary {
  color: #4361ee;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-info {
  color: #2196f3;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-success {
  color: #00ab55;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-danger {
  color: #e7515a;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-warning {
  color: #e2a03f;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-dark {
  color: #3b3f5c;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-secondary {
  color: #805dca;
  background-color: transparent;
  border: 1px solid transparent;
}
body.dark .link-badge-primary:focus, body.dark .link-badge-primary:hover {
  color: #4361ee;
  background-color: transparent;
}
body.dark .link-badge-secondary:focus, body.dark .link-badge-secondary:hover {
  color: #6f51ea;
  background-color: transparent;
}
body.dark .link-badge-success:focus, body.dark .link-badge-success:hover {
  color: #2ea37d;
  background-color: transparent;
}
body.dark .link-badge-danger:focus, body.dark .link-badge-danger:hover {
  color: #e7515a;
  background-color: transparent;
}
body.dark .link-badge-warning:focus, body.dark .link-badge-warning:hover {
  color: #dea82a;
  background-color: transparent;
}
body.dark .link-badge-info:focus, body.dark .link-badge-info:hover {
  color: #009eda;
  background-color: transparent;
}
body.dark .link-badge-dark:focus, body.dark .link-badge-dark:hover {
  color: #454656;
  background-color: transparent;
}
body.dark .avatar {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 3rem;
  font-size: 1rem;
}
body.dark .avatar--group {
  display: inline-flex;
  margin-right: 15px;
}
body.dark .avatar--group.avatar-group-badge {
  position: relative;
}
body.dark .avatar--group.avatar-group-badge .badge.counter {
  z-index: 2;
  right: 0;
  top: -6px;
  width: 21px;
  height: 21px;
  border-radius: 50%;
  padding: 5px 0px;
  font-size: 9px;
  left: -21px;
  border: none;
}
body.dark .avatar--group.avatar-group-badge .badge.counter:empty {
  display: block;
  height: 13px;
  width: 13px;
  left: -14px;
  top: 0;
}
body.dark .avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
body.dark .avatar .avatar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #506690;
  color: #fff;
}
body.dark .avatar .avatar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #805dca;
  color: #fff;
}
body.dark .avatar-icon svg {
  width: 24px;
  height: 24px;
  stroke-width: 1.7;
}
body.dark .avatar--group .avatar-xl {
  margin-left: -1.28125rem;
}
body.dark .avatar--group .avatar {
  margin-left: -0.75rem;
}
body.dark .avatar--group img, body.dark .avatar--group .avatar .avatar-title {
  border: 2px solid #888ea8;
}
body.dark .avatar-xl {
  width: 5.125rem;
  height: 5.125rem;
  font-size: 1.70833rem;
}
body.dark .avatar-xl svg {
  width: 43px;
  height: 43px;
}
body.dark .avatar-lg {
  width: 4rem;
  height: 4rem;
  font-size: 1.33333rem;
}
body.dark .avatar-lg svg {
  width: 32px;
  height: 32px;
}
body.dark .avatar-sm {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 0.83333rem;
}
body.dark .avatar-sm svg {
  width: 18px;
  height: 18px;
}
body.dark .avatar-indicators:before {
  content: "";
  position: absolute;
  bottom: 1%;
  right: 5%;
  width: 28%;
  height: 28%;
  border-radius: 50%;
  border: none;
}
body.dark .avatar-offline:before {
  background-color: #506690;
}
body.dark .avatar-online:before {
  background-color: #009688;
}
body.dark .avatar.translateY-axis img, body.dark .avatar.translateY-axis .avatar-title {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .avatar.translateY-axis img:hover, body.dark .avatar.translateY-axis .avatar-title:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
body.dark .avatar.translateX-axis img, body.dark .avatar.translateX-axis .avatar-title {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
body.dark .avatar.translateX-axis img:hover, body.dark .avatar.translateX-axis .avatar-title:hover {
  -webkit-transform: translateX(5px) scale(1.02);
  transform: translateX(5px) scale(1.02);
}
body.dark .avatar-chip {
  display: inline-block;
  padding: 0 24px;
  font-size: 16px;
  line-height: 34px;
  border-radius: 25px;
  position: relative;
}
body.dark .avatar-chip.avatar-dismiss {
  padding: 0 31px 0 25px;
}
body.dark .avatar-chip img {
  float: left;
  margin: 0px 10px 0px -26px;
  height: 35px;
  width: 35px;
  border-radius: 50%;
}
body.dark .avatar-chip span.text {
  font-size: 13px;
  font-weight: 600;
}
body.dark .avatar-chip .closebtn {
  color: #ffffff;
  font-weight: bold;
  /* float: right; */
  font-size: 15px;
  cursor: pointer;
  position: absolute;
  /* left: 0; */
  right: 8px;
}
body.dark .avatar-chip .closebtn:hover {
  color: #fff;
}
body.dark .status.rounded-tooltip .tooltip-inner {
  border-radius: 20px;
  padding: 8px 20px;
}
body.dark .tooltip-inner {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
body.dark .popover {
  z-index: 999;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border-bottom-color: #b3b3b3;
}
body.dark .help-block, body.dark .help-inline {
  color: #555555;
}
body.dark .controls {
  position: relative;
}
body.dark .table {
  color: #888ea8;
  border-collapse: separate;
  border-spacing: 0;
}
body.dark .table th .form-check, body.dark .table td .form-check {
  margin-right: 0;
  display: inline-flex;
  margin-bottom: 0;
}
body.dark .table .form-check-input {
  background-color: #515365;
  border-color: #515365;
}
body.dark .table > :not(caption) > * > * {
  color: inherit;
  background-color: inherit;
}
body.dark .table thead {
  color: #bfc9d4;
  letter-spacing: 1px;
}
body.dark .table thead tr th {
  border: none;
  background: #060818;
  padding: 10px 21px 10px 21px;
  vertical-align: middle;
  font-weight: 500;
}
body.dark .table thead tr.table-row-hidden {
  border: none;
}
body.dark .table:not(.dataTable) thead tr th:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
body.dark .table:not(.dataTable) thead tr th:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
body.dark .table:not(.dataTable) thead tr th.checkbox-area {
  width: 5%;
}
body.dark .table tbody {
  border: none;
}
body.dark .table tbody tr th {
  border: none;
}
body.dark .table tbody tr td {
  border: none;
  padding: 10px 21px 10px 21px;
  vertical-align: middle;
  letter-spacing: normal;
  white-space: nowrap;
  font-weight: 400;
  background: transparent;
}
body.dark .table > :not(:first-child) {
  border: none;
}
body.dark .table:not(.dataTable) tbody tr td svg {
  width: 17px;
  height: 17px;
  vertical-align: text-top;
  color: #4361ee;
  stroke-width: 1.5;
}
body.dark .table tbody tr td .table-inner-text {
  margin-left: 5px;
}
body.dark .table > tbody > tr > td .usr-img-frame {
  background-color: #1b2e4b;
  padding: 2px;
  width: 38px;
  height: 38px;
}
body.dark .table > tbody > tr > td .usr-img-frame img {
  width: 38px;
  margin: 0;
}
body.dark .table > tbody > tr > td .progress {
  width: 135px;
  height: 6px;
  margin: auto 0;
}
body.dark .table > tbody .action-btns .action-btn svg {
  width: 20px;
  height: 20px;
  color: #888ea8;
  stroke-width: 2;
}
body.dark .table > tbody .action-btns .action-btn:hover svg {
  color: #bfc9d4;
}
body.dark .table > tbody .action-btns .btn-delete svg {
  color: #f8538d;
}
body.dark .table > tbody .action-btns .btn-delete:hover svg {
  color: #e7515a;
}
body.dark .table-hover > tbody > tr:hover td {
  --bs-table-accent-bg:transparent;
  color: #bfc9d4;
  background-color: #1b2e4b;
  cursor: pointer;
}
body.dark .table-hover > tbody > tr:hover td:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
body.dark .table-hover > tbody > tr:hover td:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
body.dark .table-striped.table-hover > tbody > tr:hover td {
  background-color: #1b2e4b;
}
body.dark .table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td {
  --bs-table-accent-bg: transparent;
  color: #fff;
  background-color: rgba(27, 46, 75, 0.33);
}
body.dark .table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
body.dark .table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:nth-of-type(odd) td:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:nth-of-type(odd) td:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:first-child td:first-child {
  border-top-left-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:first-child td:last-child {
  border-top-right-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered.table-striped > tbody > tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered thead tr th {
  border: 1px solid #191e3a;
  background: transparent;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered > tbody > tr td {
  border: 1px solid #191e3a;
}
body.dark .table:not(.dataTable).table-bordered > tbody > tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered > tbody > tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover td:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover td:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:first-child td:first-child {
  border-top-left-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:first-child td:last-child {
  border-top-right-radius: 0;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
body.dark .table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:last-child td:last-child {
  border-bottom-right-radius: 10px;
}
body.dark .statbox .widget-content:before, body.dark .statbox .widget-content:after {
  display: table;
  content: "";
  line-height: 0;
  clear: both;
}
body.dark .nav-tabs > li > a {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
}
body.dark .nav-link {
  color: #e0e6ed;
}
body.dark .nav-link:hover {
  color: #bfc9d4;
}
body.dark .nav-link:hover svg {
  color: #bfc9d4;
}
body.dark .btn-toolbar {
  margin-left: 0px;
}
body.dark .spin {
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}
body.dark .toast-primary {
  background: #4361ee;
}
body.dark .toast-header {
  background: #4361ee;
  color: #fff;
  border-bottom: 1px solid rgba(33, 150, 243, 0.3411764706);
}
body.dark .toast-header .meta-time {
  color: #f1f2f3;
}
body.dark .toast-header .btn-close {
  color: #f1f2f3;
  opacity: 1;
  text-shadow: none;
  background: none;
  padding: 0;
  margin-top: -2px;
}
body.dark .toast-body {
  padding: 16px 12px;
  color: #fff;
}
body.dark .bg-primary {
  background-color: #4361ee !important;
  border-color: #4361ee;
  color: #fff;
}
body.dark .bg-success {
  background-color: #00ab55 !important;
  border-color: #00ab55;
  color: #fff;
}
body.dark .bg-info {
  background-color: #2196f3 !important;
  border-color: #2196f3;
  color: #fff;
}
body.dark .bg-warning {
  background-color: #e2a03f !important;
  border-color: #e2a03f;
  color: #fff;
}
body.dark .bg-danger {
  background-color: #e7515a !important;
  border-color: #e7515a;
  color: #fff;
}
body.dark .bg-secondary {
  background-color: #805dca !important;
  border-color: #805dca;
  color: #fff;
}
body.dark .bg-dark {
  background-color: #3b3f5c !important;
  border-color: #3b3f5c;
  color: #fff;
}
body.dark .bg-light-primary {
  background-color: #152143 !important;
  border-color: #152143;
  color: #2196f3;
}
body.dark .bg-light-success {
  background-color: #0c272b !important;
  border-color: #0c272b;
  color: #00ab55;
}
body.dark .bg-light-info {
  background-color: #0b2f52 !important;
  border-color: #0b2f52;
  color: #2196f3;
}
body.dark .bg-light-warning {
  background-color: #282625 !important;
  border-color: #282625;
  color: #e2a03f;
}
body.dark .bg-light-danger {
  background-color: #2c1c2b !important;
  border-color: #2c1c2b;
  color: #e7515a;
}
body.dark .bg-light-secondary {
  background-color: #1d1a3b !important;
  border-color: #1d1a3b;
  color: #805dca;
}
body.dark .bg-light-dark {
  background-color: #181e2e;
  border-color: #181e2e;
  color: #fff;
}
body.dark .progress {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background-color: #191e3a;
  margin-bottom: 1.25rem;
  height: 16px;
  box-shadow: none;
}
body.dark .progress.progress-bar-stack .progress-bar:last-child {
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
}
body.dark .progress .progress-bar {
  font-size: 10px;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(0, 69, 255, 0.15), 0 8px 16px rgba(0, 69, 255, 0.2);
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: 100;
}
body.dark .progress:not(.progress-bar-stack) .progress-bar {
  border-radius: 16px;
}
body.dark .progress-sm {
  height: 4px;
}
body.dark .progress-md {
  height: 10px;
}
body.dark .progress-lg {
  height: 20px;
}
body.dark .progress-xl {
  height: 25px;
}
body.dark .progress-striped .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
body.dark .progress .progress-title {
  display: flex;
  justify-content: space-between;
  padding: 15px;
}
body.dark .progress .progress-title span {
  align-self: center;
}
body.dark .progress .progress-bar.bg-gradient-primary {
  background-color: #4361ee;
  background: linear-gradient(to right, #0081ff 0%, #0045ff 100%);
}
body.dark .progress .progress-bar.bg-gradient-info {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #04befe 0%, #4481eb 100%);
}
body.dark .progress .progress-bar.bg-gradient-success {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #3cba92 0%, #0ba360 100%);
}
body.dark .progress .progress-bar.bg-gradient-warning {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #f09819 0%, #ff5858 100%);
}
body.dark .progress .progress-bar.bg-gradient-secondary {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #7579ff 0%, #b224ef 100%);
}
body.dark .progress .progress-bar.bg-gradient-danger {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #d09693 0%, #c71d6f 100%);
}
body.dark .progress .progress-bar.bg-gradient-dark {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #2b5876 0%, #4e4376 100%);
}
body.dark .page-meta {
  margin-top: 25px;
}
body.dark .page-meta .breadcrumb .breadcrumb-item {
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 1px;
}
body.dark .page-meta .breadcrumb .breadcrumb-item a {
  vertical-align: inherit;
}
body.dark .page-meta .breadcrumb .breadcrumb-item.active {
  font-weight: 500;
}
body.dark .breadcrumb {
  background-color: transparent;
  margin-bottom: 0;
}
body.dark .breadcrumb-wrapper-content {
  background-color: rgb(26, 28, 45);
  padding: 13px 23px;
  border-radius: 8px;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
body.dark .breadcrumb .breadcrumb-item a {
  color: #e0e6ed;
  vertical-align: text-bottom;
  vertical-align: text-top;
}
body.dark .breadcrumb .breadcrumb-item.active a {
  color: #515365;
}
body.dark .breadcrumb .breadcrumb-item a svg {
  width: 19px;
  height: 19px;
  vertical-align: sub;
  stroke-width: 1.4px;
}
body.dark .breadcrumb .breadcrumb-item a .inner-text {
  margin-left: 10px;
}
body.dark .breadcrumb .breadcrumb-item span {
  vertical-align: text-bottom;
}
body.dark .breadcrumb .breadcrumb-item::before {
  color: inherit;
}
body.dark .breadcrumb .breadcrumb-item.active {
  color: #d3d3d3;
  font-weight: 600;
}
body.dark .breadcrumb-style-two .breadcrumb-item + .breadcrumb-item::before {
  content: ".";
  position: relative;
  top: -9px;
  font-size: 21px;
  height: 7px;
}
body.dark .breadcrumb-style-three .breadcrumb-item + .breadcrumb-item::before {
  content: "-";
}
body.dark .breadcrumb-style-four .breadcrumb-item + .breadcrumb-item::before {
  content: "|";
}
body.dark .breadcrumb-style-five .breadcrumb-item + .breadcrumb-item::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right' style='color: %23888ea8;'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  color: #6E6B7B;
  margin-right: 0.6rem;
  background-size: 12px;
  height: 20px;
}
body.dark .br-0 {
  border-radius: 0 !important;
}
body.dark .br-4 {
  border-radius: 4px !important;
}
body.dark .br-6 {
  border-radius: 6px !important;
}
body.dark .br-8 {
  border-radius: 8px !important;
}
body.dark .br-30 {
  border-radius: 30px !important;
}
body.dark .br-50 {
  border-radius: 50px !important;
}
body.dark .br-left-30 {
  border-top-left-radius: 30px !important;
  border-bottom-left-radius: 30px !important;
}
body.dark .br-right-30 {
  border-top-right-radius: 30px !important;
  border-bottom-right-radius: 30px !important;
}
body.dark .bx-top-6 {
  border-top-right-radius: 6px !important;
  border-top-left-radius: 6px !important;
}
body.dark .bx-bottom-6 {
  border-bottom-right-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}
body.dark .badge.counter {
  position: absolute;
  z-index: 2;
  right: 0;
  top: -10px;
  font-weight: 600;
  width: 19px;
  height: 19px;
  border-radius: 50%;
  padding: 2px 0px;
  font-size: 12px;
}
body.dark .text-primary {
  color: #4361ee !important;
}
body.dark .text-success {
  color: #00ab55 !important;
}
body.dark .text-info {
  color: #2196f3 !important;
}
body.dark .text-danger {
  color: #e7515a !important;
}
body.dark .text-warning {
  color: #e2a03f !important;
}
body.dark .text-secondary {
  color: #805dca !important;
}
body.dark .text-dark {
  color: #3b3f5c !important;
}
body.dark .text-muted {
  color: #888ea8 !important;
}
body.dark .text-white {
  color: #fff !important;
}
body.dark .text-black {
  color: #000 !important;
}
body.dark .border {
  border: 1px solid !important;
}
body.dark .border-bottom {
  border-bottom: 1px solid !important;
}
body.dark .border-top {
  border-top: 1px solid !important;
}
body.dark .border-right {
  border-right: 1px solid !important;
}
body.dark .border-left {
  border-left: 1px solid !important;
}
body.dark .border-primary {
  border-color: #4361ee !important;
}
body.dark .border-info {
  border-color: #2196f3 !important;
}
body.dark .border-warning {
  border-color: #e2a03f !important;
}
body.dark .border-success {
  border-color: #00ab55 !important;
}
body.dark .border-danger {
  border-color: #e7515a !important;
}
body.dark .border-secondary {
  border-color: #805dca !important;
}
body.dark .border-dark {
  border-color: #3b3f5c !important;
}
body.dark .border-dotted {
  border-style: dotted !important;
}
body.dark .border-dashed {
  border-style: dashed !important;
}
body.dark .border-solid {
  border-style: solid !important;
}
body.dark .border-double {
  border-style: double !important;
}
body.dark .border-width-1px {
  border-width: 1px !important;
}
body.dark .border-width-2px {
  border-width: 2px !important;
}
body.dark .border-width-3px {
  border-width: 3px !important;
}
body.dark .border-width-4px {
  border-width: 4px !important;
}
body.dark .border-width-5px {
  border-width: 5px !important;
}
body.dark .border-width-6px {
  border-width: 6px !important;
}
body.dark .position-absolute {
  position: absolute;
}
body.dark .position-static {
  position: static;
}
body.dark .position-fixed {
  position: fixed;
}
body.dark .position-inherit {
  position: inherit;
}
body.dark .position-initial {
  position: initial;
}
body.dark .position-relative {
  position: relative;
}

/*
    Btn group dropdown-toggle
*/
/* Primary */
/* Light Buttons  */
/* Primary */
/*      Dropdown Toggle       */
/*
    ===========================
        Checkboxes and Radio
    ===========================
*/
/*
    =================
        Switches
    =================
*/
/*
    ===========================
        Data Marker ( dot )
    ===========================
*/
/*      Link     */
/*
	Indicators
*/
/*      Avatar      */
/* .search-form-control { border-radius: .25rem; } */
/*  
    ====================
        Table
    ====================
*/
/*

    Hover

*/
/* 
    Hover and Striped
*/
/* 

    Striped

*/
/* 
    Striped and Bordered
*/
/* 

    Bordered

*/
/* 
    Bordered and Hover
*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  body.dark .input-group > .form-control {
    flex: 1 1 auto;
    width: 1%;
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/*  
    ==========================
        Background Colors  
    ==========================
*/
/*  
    Default  
*/
/*  
    Light Background  
*/
/*  
    Progress Bar
*/
/* 
    =====================
        BreadCrumbs
    =====================
*/
/*
    Style Two
*/
/*
    Style Three
*/
/*
    Style Four
*/
/*
    Style Five
*/
/*      Badge Custom      */
/*-------text-colors------*/
/*-----border main------*/
/*-----border style------*/
/*-----border width------*/
/*-----transform-position------*/
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
