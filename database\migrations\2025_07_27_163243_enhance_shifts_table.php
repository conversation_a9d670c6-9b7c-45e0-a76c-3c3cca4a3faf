<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shifts', function (Blueprint $table) {
            $table->string('shift_type')->default('regular')->after('status');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('shift_type');
            $table->text('notes')->nullable()->after('hourly_rate');
            $table->boolean('reminder_sent')->default(false)->after('notes');
            $table->foreignId('created_by')->nullable()->constrained('users')->after('reminder_sent');
            $table->string('recurring_pattern')->nullable()->after('created_by'); // daily, weekly, monthly
            $table->datetime('recurring_until')->nullable()->after('recurring_pattern');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shifts', function (Blueprint $table) {
            $table->dropColumn([
                'shift_type',
                'hourly_rate',
                'notes',
                'reminder_sent',
                'created_by',
                'recurring_pattern',
                'recurring_until'
            ]);
        });
    }
};
