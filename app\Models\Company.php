<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Company extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'email_address', 'timezone', 'address', 'phone_number', 'status'];

    public function users()
    {
        return $this->belongsToMany(User::class)->withPivot('hourly_rate')->withTimestamps();
    }
    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }
    public function staffLogins()
    {
        return $this->hasMany(StaffLogin::class);
    }
}
