<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Company;
use App\Models\User;

class StaffLogin extends Model
{
    protected $fillable = [
        'user_id',
        'company_id',
        'login_time',
        'logout_time',
        'rate_type',
        'shift_status',
        'status',
        'pause_periods',
        'total_pause_minutes',
        'current_pause_reason',
        'current_pause_start'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    protected $casts = [
        'login_time' => 'datetime',
        'logout_time' => 'datetime',
        'current_pause_start' => 'datetime',
        'pause_periods' => 'array',
    ];

    /**
     * Get the total work hours excluding pause time
     */
    public function getWorkHoursAttribute()
    {
        if (!$this->login_time) {
            return 0;
        }

        $endTime = $this->logout_time ?? now();
        $totalMinutes = $this->login_time->diffInMinutes($endTime);

        return max(0, ($totalMinutes - $this->total_pause_minutes) / 60);
    }

    /**
     * Get the current session duration in minutes
     */
    public function getCurrentSessionMinutes()
    {
        if (!$this->login_time) {
            return 0;
        }

        $endTime = $this->logout_time ?? now();
        $totalMinutes = $this->login_time->diffInMinutes($endTime);

        // Subtract pause time if currently paused
        if ($this->shift_status === 'paused' && $this->current_pause_start) {
            $currentPauseMinutes = $this->current_pause_start->diffInMinutes(now());
            return max(0, $totalMinutes - $this->total_pause_minutes - $currentPauseMinutes);
        }

        return max(0, $totalMinutes - $this->total_pause_minutes);
    }

}
