<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Shift;
use App\Models\Company;
use Illuminate\Support\Facades\Auth;

class ShiftController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Shift::with(['user', 'company', 'createdBy']);

        // Apply filters
        if ($request->filled('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('start_time', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('start_time', '<=', $request->date_to);
        }

        $shifts = $query->latest()->paginate(15);

        // Get filter options
        $companies = Company::all();
        $users = User::role('Staff')->get();

        return view('Admin.shifts.index', compact('shifts', 'companies', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::role('Staff')->get();
        $companies = Company::all();
        return view('Admin.shifts.create', compact('users', 'companies'));
    }

    /**
     * Show bulk assignment form
     */
    public function bulkCreate()
    {
        $users = User::role('Staff')->get();
        $companies = Company::all();
        return view('Admin.shifts.bulk-create', compact('users', 'companies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'company_id' => 'required|exists:companies,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'shift_type' => 'required|in:regular,overtime,holiday',
            'hourly_rate' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'recurring_pattern' => 'nullable|in:daily,weekly,monthly',
            'recurring_until' => 'nullable|date|after:start_time',
        ]);

        $data = $request->all();
        $data['created_by'] = Auth::id();

        // Get company-specific hourly rate if not provided
        if (!$data['hourly_rate']) {
            $user = User::find($data['user_id']);
            $company = $user->companies()->where('companies.id', $data['company_id'])->first();
            $data['hourly_rate'] = $company->pivot->hourly_rate ?? 0;
        }

        $shift = Shift::create($data);

        // Handle recurring shifts
        if ($request->recurring_pattern && $request->recurring_until) {
            $this->createRecurringShifts($shift, $request);
        }

        // Send notification to assigned staff
        $shift->user->notify(new \App\Notifications\ShiftAssignedNotification($shift));

        return redirect()->route('shifts.index')->with('success', 'Shift assigned successfully.');
    }

    /**
     * Store bulk shifts
     */
    public function bulkStore(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'company_id' => 'required|exists:companies,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'shift_type' => 'required|in:regular,overtime,holiday',
            'hourly_rate' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        $createdShifts = [];

        foreach ($request->user_ids as $userId) {
            $user = User::find($userId);
            $company = $user->companies()->where('companies.id', $request->company_id)->first();

            if (!$company) {
                continue; // Skip if user doesn't belong to this company
            }

            $hourlyRate = $request->hourly_rate ?: ($company->pivot->hourly_rate ?? 0);

            $shift = Shift::create([
                'user_id' => $userId,
                'company_id' => $request->company_id,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'shift_type' => $request->shift_type,
                'hourly_rate' => $hourlyRate,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            $createdShifts[] = $shift;

            // Send notification
            $user->notify(new \App\Notifications\ShiftAssignedNotification($shift));
        }

        return redirect()->route('shifts.index')
            ->with('success', count($createdShifts) . ' shifts assigned successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $shift = Shift::with(['user', 'company', 'createdBy'])->findOrFail($id);
        return view('Admin.shifts.show', compact('shift'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $shift = Shift::findOrFail($id);
        $shift->delete();

        return redirect()->route('shifts.index')->with('success', 'Shift deleted successfully.');
    }

    /**
     * Create recurring shifts
     */
    private function createRecurringShifts(Shift $originalShift, Request $request)
    {
        $pattern = $request->recurring_pattern;
        $until = \Carbon\Carbon::parse($request->recurring_until);
        $current = \Carbon\Carbon::parse($originalShift->start_time);
        $endTime = \Carbon\Carbon::parse($originalShift->end_time);
        $duration = $current->diffInMinutes($endTime);

        while ($current->lt($until)) {
            switch ($pattern) {
                case 'daily':
                    $current->addDay();
                    break;
                case 'weekly':
                    $current->addWeek();
                    break;
                case 'monthly':
                    $current->addMonth();
                    break;
            }

            if ($current->lte($until)) {
                $newEndTime = $current->copy()->addMinutes($duration);

                Shift::create([
                    'user_id' => $originalShift->user_id,
                    'company_id' => $originalShift->company_id,
                    'start_time' => $current->copy(),
                    'end_time' => $newEndTime,
                    'shift_type' => $originalShift->shift_type,
                    'hourly_rate' => $originalShift->hourly_rate,
                    'notes' => $originalShift->notes,
                    'created_by' => $originalShift->created_by,
                    'recurring_pattern' => $pattern,
                    'recurring_until' => $until,
                ]);
            }
        }
    }

    /**
     * Send shift reminders
     */
    public function sendReminders()
    {
        $shifts = Shift::needingReminder()->with('user')->get();

        foreach ($shifts as $shift) {
            $shift->user->notify(new \App\Notifications\ShiftReminderNotification($shift));
            $shift->update(['reminder_sent' => true]);
        }

        return response()->json([
            'success' => true,
            'message' => count($shifts) . ' reminders sent successfully.'
        ]);
    }
}
