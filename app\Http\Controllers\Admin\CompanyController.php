<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Company;
use App\Models\User;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $companies = Company::with('users')->latest()->get();
        return view('Admin.Company.index', compact('companies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::all();
        return view('Admin.Company.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'email_address' => 'required|email',
            'address' => 'nullable|string',
            'phone_number' => 'nullable|string',
            'status' => 'required',
            // 'users' => 'nullable|array'
        ]);

        $company = Company::create($validated);
        // $company->users()->sync($request->users);

        return redirect()->route('companies.index')->with('success', 'Company created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $company = Company::findOrFail($id);
        // $users = User::all();
        return view('Admin.Company.edit', compact('company'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'email_address' => 'required|email',
            'address' => 'nullable|string',
            'phone_number' => 'nullable|string',
            'status' => 'required',
            // 'users' => 'nullable|array'
        ]);
        $validated['status'] = $validated['status'] === 'active' ? 1 : 0;

        $company = Company::findOrFail($id);
        $company->update($validated);
        // $company->users()->sync($request->users);

        return redirect()->route('companies.index')->with('success', 'Company updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $company = Company::findOrFail($id);
        // $company->users()->detach();
        $company->delete();

        return redirect()->route('companies.index')->with('success', 'Company deleted successfully.');
    }
}
