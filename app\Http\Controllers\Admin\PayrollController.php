<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PayrollCalculatorService;
use Carbon\Carbon;
use App\Exports\PayrollExport;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class PayrollController extends Controller
{
    // public function showPayroll($userId)
    // {
    //     // dd($userId);
    //     $from = Carbon::parse('2025-06-13');
    //     $to = Carbon::parse('2025-06-13');

    //     $payrollService = new PayrollCalculatorService();
    //     $hourSummary = $payrollService->calculateUserHours($userId, $from, $to);

    //     return view('admin.payroll.show', compact('hourSummary','userId'));
    // }

    // public function exportExcel($userId)
    // {
    //     $from = Carbon::parse('2025-06-13');
    //     $to = Carbon::parse('2025-06-13');
    //     $hourSummary = (new PayrollCalculatorService)->calculateUserHours($userId, $from, $to);

    //     return Excel::download(new PayrollExport($hourSummary), 'payroll_report.xlsx');
    // }

    // public function exportPdf($userId)
    // {
    //     $from = Carbon::parse('2025-06-13');
    //     $to = Carbon::parse('2025-06-13');
    //     $hourSummary = (new PayrollCalculatorService)->calculateUserHours($userId, $from, $to);

    //     $pdf = Pdf::loadView('admin.payroll.export_pdf', compact('hourSummary'))->setPaper('a4', 'landscape');
    //     return $pdf->download('payroll_report.pdf');
    // }

    // use Carbon\Carbon;

    public function showPayroll(Request $request, $userId)
    {
        $from = Carbon::parse($request->query('from', now()->startOfMonth()));
        $to = Carbon::parse($request->query('to', now()));

        $payrollService = new PayrollCalculatorService();
        $hourSummary = $payrollService->calculateUserHours($userId, $from, $to);

        return view('admin.payroll.show', compact('hourSummary', 'userId', 'from', 'to'));
    }

    public function exportExcel(Request $request, $userId)
    {
        $from = Carbon::parse($request->query('from', now()->startOfMonth()));
        $to = Carbon::parse($request->query('to', now()));

        $hourSummary = (new PayrollCalculatorService)->calculateUserHours($userId, $from, $to);
        return Excel::download(new PayrollExport($hourSummary), 'payroll_report.xlsx');
    }

    public function exportPdf(Request $request, $userId)
    {
        $from = Carbon::parse($request->query('from', now()->startOfMonth()));
        $to = Carbon::parse($request->query('to', now()));

        $hourSummary = (new PayrollCalculatorService)->calculateUserHours($userId, $from, $to);
        $pdf = Pdf::loadView('admin.payroll.export_pdf', compact('hourSummary'))->setPaper('a4', 'landscape');
        return $pdf->download('payroll_report.pdf');
    }
}
