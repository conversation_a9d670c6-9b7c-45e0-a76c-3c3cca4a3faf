@extends('layouts.app')

@section('content')
    <div class="container mt-4">
        <h4 class="fw-bold mb-4">Hello <span class="text-primary">{{ Auth::user()->name }}!</span></h4>

        <div class="alert-list">
            @forelse($notifications as $notification)
                <div class="card mb-3 shadow-sm border-0">
                    <div class="card-body d-flex">
                        <img src="https://i.pravatar.cc/50?u={{ $notification->id }}" class="rounded-circle me-3"
                            alt="Avatar">
                        <div class="w-100">
                            <h6 class="mb-1 fw-semibold">{{ $notification->data['title'] ?? 'Notification' }}</h6>
                            <p class="mb-0">{{ $notification->data['message'] ?? '' }}</p>

                            @if (isset($notification->data['start_time']) && isset($notification->data['end_time']))
                                <p class="text-muted small mb-1">
                                    {{ \Carbon\Carbon::parse($notification->data['start_time'])->format('F j, Y – h:i A') }}
                                    to
                                    {{ \Carbon\Carbon::parse($notification->data['end_time'])->format('h:i A') }}
                                </p>
                            @endif

                            <small class="text-muted">
                                {{ $notification->created_at->diffForHumans() }}
                            </small>

                            <form action="{{ route('notifications.markAsRead', $notification->id) }}" method="POST"
                                class="mt-2">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-sm btn-outline-primary">Mark as Read</button>
                            </form>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center text-muted">No new notifications.</div>
            @endforelse
        </div>
    </div>
@endsection
