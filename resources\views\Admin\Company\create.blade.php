@extends('layouts.app')

@section('content')
    <form method="POST" action="{{ route('companies.store') }}">
        @csrf
        <div class="mb-3">
            <label>Company Name</label>
            <input type="text" name="name" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>Email Address</label>
            <input type="email" name="email_address" class="form-control" required>
        </div>
        {{-- <div class="mb-3">
        <label>Timezone</label>
        <input type="text" name="timezone" class="form-control" required>
    </div> --}}
        <div class="mb-3">
            <label>Address</label>
            <input type="text" name="address" class="form-control">
        </div>
        <div class="mb-3">
            <label>Phone Number</label>
            <input type="text" name="phone_number" class="form-control">
        </div>
        <div class="mb-3">
            <label>Status</label>
            <select name="status" class="form-control">
                <option value="1">Active</option>
                <option value="0">Inactive</option>
            </select>
        </div>
        {{-- <div class="mb-3">
        <label>Assign Users</label>
        <select name="users[]" class="form-control" multiple>
            @foreach ($users as $user)
                <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
            @endforeach
        </select>
    </div> --}}
        <button type="submit" class="btn btn-primary">Create Company</button>
    </form>
@endsection
