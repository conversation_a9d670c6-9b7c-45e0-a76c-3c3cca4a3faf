@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <h4 class="mb-3">Application Review</h4>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Staff</th>
                <th>Type</th>
                <th>Title</th>
                <th>Date</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
        @foreach($applications as $application)
            <tr>
                <td>{{ $application->user->name }}</td>
                <td>{{ $application->applicationType->name ?? 'N/A' }}</td>
                <td>{{ $application->title }}</td>
                <td>{{ $application->application_date }}</td>
                <td>
                    <span class="badge bg-{{ $application->status === 'approved' ? 'success' : ($application->status === 'rejected' ? 'danger' : 'secondary') }}">
                        {{ ucfirst($application->status) }}
                    </span>
                </td>
                <td>
                    @if($application->status === 'pending')
                        <form method="POST" action="{{ route('applications.updateStatus', $application->id) }}">
                            @csrf
                            @method('PUT')
                            <button name="status" value="approved" class="btn btn-success btn-sm">Approve</button>
                            <button name="status" value="rejected" class="btn btn-danger btn-sm">Reject</button>
                        </form>
                    @else
                        <small>Reviewed</small>
                    @endif
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
@endsection
