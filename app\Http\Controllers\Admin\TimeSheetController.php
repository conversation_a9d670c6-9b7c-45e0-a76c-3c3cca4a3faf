<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StaffLogin;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TimeSheetController extends Controller
{
    public function staffTimeSheet()
    {
        $user = Auth::user();
        // dd($user);

        // Get all logins
        $logins = StaffLogin::with('shift')
            ->where('user_id', $user->id)
            ->orderByDesc('login_time')
            ->take(7)
            ->get();

        // Attendance Summary
        $total = $logins->count();
        $present = $logins->whereNotNull('login_time')->count();
        $leave = 0;
        $absent = $total - $present;

        // Monthly Worked Hours Summary
        $monthlyWorked = StaffLogin::where('user_id', $user->id)
            ->whereNotNull('login_time')
            ->whereNotNull('logout_time')
            ->get()
            ->groupBy(function ($item) {
                return Carbon::parse($item->login_time)->format('M');
            })
            ->map(function ($group) {
                return $group->sum(function ($item) {
                    return Carbon::parse($item->logout_time)->diffInMinutes(Carbon::parse($item->login_time));
                });
            });

        return view('staff.staff_time_sheet', compact('logins', 'present', 'absent', 'leave', 'total', 'monthlyWorked'));
    }

    public function clockIn()
    {
        $user = Auth::user();

        $todayShift = Shift::where('user_id', $user->id)
            ->whereDate('start_time', Carbon::today())
            ->first();

        $login = StaffLogin::create([
            'user_id' => $user->id,
            'company_id' => $user->company_id,
            'shift_id' => $todayShift?->id,
            'login_time' => now(),
        ]);

        return redirect()->back()->with('success', 'Clocked in successfully.');
    }

    public function clockOut()
    {
        $user = Auth::user();

        $login = StaffLogin::where('user_id', $user->id)
            ->whereDate('login_time', Carbon::today())
            ->whereNull('logout_time')
            ->first();

        if (!$login) {
            return redirect()->back()->with('error', 'No active clock-in found.');
        }

        $login->logout_time = now();

        $shift = $login->shift;
        $status = 'on_time';

        if ($shift) {
            if ($login->login_time > $shift->start_time) {
                $status = 'late_in';
            }
            if ($login->logout_time < $shift->end_time) {
                $status = $status === 'late_in' ? 'late_in' : 'left_early';
            }
        }

        $login->status = $status;
        $login->save();

        return redirect()->back()->with('success', 'Clocked out successfully.');
    }
}
