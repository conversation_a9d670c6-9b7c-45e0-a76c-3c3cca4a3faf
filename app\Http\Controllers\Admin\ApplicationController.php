<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\ApplicationType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

class ApplicationController extends Controller
{
    public function index()
    {
        $applications = Application::with('applicationType')
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(8);

        return view('staff.index_application', compact('applications'));
    }

    public function create()
    {
        $applicationTypes = ApplicationType::all();
        return view('staff.create_application', compact('applicationTypes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'application_type_id' => 'required|exists:application_types,id',
            'title' => 'required|string|max:255',
            'application_date' => 'required|date',
            'description' => 'required|string',
        ]);

        Application::create([
            'user_id' => Auth::id(),
            'application_type_id' => $request->application_type_id,
            'title' => $request->title,
            'application_date' => $request->application_date,
            'description' => $request->description,
        ]);

        return redirect()->route('applications.index')->with('success', 'Application submitted successfully!');
    }


    //admin will update the status of application only of staf
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:approved,rejected',
        ]);

        $application = Application::findOrFail($id);
        $application->status = $request->status;
        $application->save();

        return back()->with('success', 'Application status updated successfully!');
    }

    //admin review application
    public function adminIndex(Request $request)
    {
        $applications = Application::with(['user', 'applicationType'])
        ->whereHas('user.roles', function ($q) {
            $q->where('name', 'staff');
        })
        ->when($request->status, fn($q) => $q->where('status', $request->status))
        ->latest()
        ->paginate(10);

    return view('Admin.applications.index', compact('applications'));
    }
}
