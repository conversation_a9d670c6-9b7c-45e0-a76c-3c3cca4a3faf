@extends('layouts.app')

@section('content')
<div class="card max-w-xl mx-auto">
    <div class="card-header">
        <h3 class="text-lg font-semibold">Edit Attendance</h3>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('attendance.update', $staffLogin) }}">
            @csrf @method('PUT')

            <div class="mb-4">
                <label class="form-label">Login Time</label>
                <input type="datetime-local" name="login_time" class="form-input" value="{{ old('login_time', \Carbon\Carbon::parse($staffLogin->login_time)->format('Y-m-d\TH:i')) }}" required>
            </div>

            <div class="mb-4">
                <label class="form-label">Logout Time</label>
                <input type="datetime-local" name="logout_time" class="form-input" value="{{ old('logout_time', $staffLogin->logout_time ? \Carbon\Carbon::parse($staffLogin->logout_time)->format('Y-m-d\TH:i') : '') }}">
            </div>

            <div class="mb-4">
                <label class="form-label">Rate Type</label>
                <select name="rate_type" class="form-select">
                    @foreach(['regular', 'double', 'triple'] as $rate)
                        <option value="{{ $rate }}" @selected($staffLogin->rate_type == $rate)>{{ ucfirst($rate) }}</option>
                    @endforeach
                </select>
            </div>

            <div class="flex justify-between">
                <a href="{{ route('attendance.index') }}" class="btn btn-secondary">Back</a>
                <button type="submit" class="btn btn-primary">Update</button>
            </div>
        </form>
    </div>
</div>
@endsection
