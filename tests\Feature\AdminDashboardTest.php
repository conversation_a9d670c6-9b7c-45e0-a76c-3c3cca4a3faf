<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Company;
use App\Models\Shift;
use App\Models\Application;
use App\Models\StaffLogin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'Admin']);
        Role::create(['name' => 'Staff']);
        Role::create(['name' => 'Team Leader']);
    }

    public function test_admin_can_access_dashboard()
    {
        // Create an admin user
        $admin = User::factory()->create();
        $admin->assignRole('Admin');

        // Create some test data
        $company = Company::factory()->create();
        $staff = User::factory()->create();
        $staff->assignRole('Staff');

        // Act as admin and visit dashboard
        $response = $this->actingAs($admin)->get('/dashboard');

        // Assert the response is successful
        $response->assertStatus(200);
        $response->assertViewIs('dashboard.admin');
        $response->assertSee('Admin Dashboard');
        $response->assertSee('Total Users');
        $response->assertSee('Staff Members');
        $response->assertSee('Companies');
    }

    public function test_dashboard_displays_correct_statistics()
    {
        // Create an admin user
        $admin = User::factory()->create();
        $admin->assignRole('Admin');

        // Create test data
        $company = Company::factory()->create(['status' => 'active']);
        $staff1 = User::factory()->create();
        $staff1->assignRole('Staff');
        $staff2 = User::factory()->create();
        $staff2->assignRole('Staff');

        // Act as admin and visit dashboard
        $response = $this->actingAs($admin)->get('/dashboard');

        // Check if statistics are displayed correctly
        $response->assertStatus(200);
        
        // Should show 3 total users (admin + 2 staff)
        $response->assertSee('3'); // Total users
        $response->assertSee('2'); // Staff members
        $response->assertSee('1'); // Companies
    }

    public function test_non_admin_cannot_access_admin_dashboard()
    {
        // Create a staff user
        $staff = User::factory()->create();
        $staff->assignRole('Staff');

        // Try to access admin dashboard
        $response = $this->actingAs($staff)->get('/dashboard');

        // Should not see admin dashboard
        $response->assertStatus(200);
        $response->assertViewIs('dashboard.staff');
    }

    public function test_dashboard_handles_empty_data_gracefully()
    {
        // Create an admin user only
        $admin = User::factory()->create();
        $admin->assignRole('Admin');

        // Act as admin and visit dashboard with no additional data
        $response = $this->actingAs($admin)->get('/dashboard');

        // Should still work with zero values
        $response->assertStatus(200);
        $response->assertSee('Admin Dashboard');
        $response->assertSee('No recent shifts');
        $response->assertSee('No recent applications');
        $response->assertSee('No logins today');
    }
}
