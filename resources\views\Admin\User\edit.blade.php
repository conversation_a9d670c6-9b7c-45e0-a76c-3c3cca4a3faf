@extends('layouts.app')

@section('content')
    <div class="container">
        <h3>Edit User</h3>
        <form action="{{ route('users.update', $user->id) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="mb-3">
                <label>Name</label>
                <input name="name" class="form-control" value="{{ $user->name }}" required>
            </div>

            <div class="mb-3">
                <label>Email</label>
                <input name="email" type="email" class="form-control" value="{{ $user->email }}" required>
            </div>

            <div class="mb-3">
                <label>New Password (optional)</label>
                <input name="password" type="password" class="form-control">
            </div>

            <div class="mb-3">
                <label>Confirm Password</label>
                <input name="password_confirmation" type="password" class="form-control">
            </div>

            <div class="mb-3">
                <label>Assign Companies</label>
                <select name="company_ids[]" multiple class="form-control" required>
                    @foreach ($companies as $company)
                        <option value="{{ $company->id }}"
                            {{ in_array($company->id, $user->companies->pluck('id')->toArray()) ? 'selected' : '' }}>
                            {{ $company->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="mb-3">
                <label for="hourly_rate" class="form-label">Hourly Rate</label>
                <input name="hourly_rate" id="hourly_rate" type="number" step="0.01" class="form-control"
                    value="{{ old('hourly_rate', optional($user->companies->first())->pivot->hourly_rate) }}">
            </div>

            <div class="mb-3">
                <label>Assign Role</label>
                <select name="role" class="form-control" required>
                    @foreach ($roles as $role)
                        <option value="{{ $role->name }}"
                            {{ $user->roles->first()?->name === $role->name ? 'selected' : '' }}>
                            {{ ucfirst($role->name) }}
                        </option>
                    @endforeach
                </select>
            </div>

            <button class="btn btn-primary">Update User</button>
        </form>
    </div>
@endsection
