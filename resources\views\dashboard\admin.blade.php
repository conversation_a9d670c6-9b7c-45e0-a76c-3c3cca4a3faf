@extends('layouts.app')

@push('styles')
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link rel="stylesheet" type="text/css" href="src/assets/css/light/elements/alert.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/dark/elements/alert.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/light/dashboard/dash_1.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/dark/dashboard/dash_1.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(233, 168, 82, 0.3);
        }

        .dashboard-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .dashboard-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #E9A852;
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-card .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #E9A852, #D4941F);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .stat-card .stat-icon i {
            font-size: 24px;
            color: white;
        }

        .stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }

        .stat-card .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .stat-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .stat-change.positive {
            color: #27ae60;
        }

        .stat-change.negative {
            color: #e74c3c;
        }

        .activity-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            height: 100%;
        }

        .activity-card h5 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #E9A852;
        }

        .activity-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            align-items: center;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            font-size: 0.9rem;
        }

        .activity-subtitle {
            color: #7f8c8d;
            margin: 0;
            font-size: 0.8rem;
        }

        .activity-time {
            color: #95a5a6;
            font-size: 0.75rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.completed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-badge.missed {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .dashboard-header h1 {
                font-size: 2rem;
            }

            .stat-card {
                margin-bottom: 1rem;
            }

            .stat-card .stat-number {
                font-size: 2rem;
            }
        }
    </style>
@endpush

@section('content')
<div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="text-white">Admin Dashboard</h1>
        <p class="text-white">Welcome back! Here's what's happening with your taxi portal today.</p>
    </div>

    <!-- Statistics Cards Row 1 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="stat-number">{{ $totalUsers }}</h3>
                <p class="stat-label">Total Users</p>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> Active system
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <h3 class="stat-number">{{ $totalStaff }}</h3>
                <p class="stat-label">Staff Members</p>
                <div class="stat-change positive">
                    <i class="fas fa-user-plus"></i> {{ $activeStaffLogins }} active
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-building"></i>
                </div>
                <h3 class="stat-number">{{ $totalCompanies }}</h3>
                <p class="stat-label">Companies</p>
                <div class="stat-change positive">
                    <i class="fas fa-check-circle"></i> {{ $activeCompanies }} active
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h3 class="stat-number">{{ $todayShifts }}</h3>
                <p class="stat-label">Today's Shifts</p>
                <div class="stat-change">
                    <i class="fas fa-clock"></i> {{ $upcomingShifts }} upcoming
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards Row 2 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3 class="stat-number">{{ $totalApplications }}</h3>
                <p class="stat-label">Total Applications</p>
                <div class="stat-change {{ $pendingApplications > 0 ? 'negative' : 'positive' }}">
                    <i class="fas fa-hourglass-half"></i> {{ $pendingApplications }} pending
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-double"></i>
                </div>
                <h3 class="stat-number">{{ $completedShifts }}</h3>
                <p class="stat-label">Completed Shifts</p>
                <div class="stat-change positive">
                    <i class="fas fa-chart-line"></i> Performance tracking
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="stat-number">{{ $missedShifts }}</h3>
                <p class="stat-label">Missed Shifts</p>
                <div class="stat-change {{ $missedShifts > 0 ? 'negative' : 'positive' }}">
                    <i class="fas fa-{{ $missedShifts > 0 ? 'arrow-down' : 'check' }}"></i>
                    {{ $missedShifts > 0 ? 'Needs attention' : 'All good' }}
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h3 class="stat-number">{{ $todayLogins }}</h3>
                <p class="stat-label">Today's Logins</p>
                <div class="stat-change positive">
                    <i class="fas fa-users"></i> {{ $activeStaffLogins }} currently active
                </div>
            </div>
        </div>
    </div>

    <!-- Activity and Recent Data -->
    <div class="row">
        <div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 mb-4">
            <div class="activity-card">
                <h5><i class="fas fa-clock text-warning me-2"></i>Recent Shifts</h5>
                @if($recentShifts->count() > 0)
                    @foreach($recentShifts as $shift)
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-calendar text-primary"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-title">{{ $shift->user->name }}</p>
                            <p class="activity-subtitle">{{ $shift->company->name }}</p>
                            <span class="status-badge {{ $shift->status }}">{{ ucfirst($shift->status) }}</span>
                        </div>
                        <div class="activity-time">
                            {{ $shift->start_time->format('M d, H:i') }}
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No recent shifts</p>
                    </div>
                @endif
            </div>
        </div>

        <div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 mb-4">
            <div class="activity-card">
                <h5><i class="fas fa-file-alt text-info me-2"></i>Recent Applications</h5>
                @if($recentApplications->count() > 0)
                    @foreach($recentApplications as $application)
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-file text-info"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-title">{{ $application->title }}</p>
                            <p class="activity-subtitle">{{ $application->user->name }}</p>
                            <span class="status-badge {{ $application->status ?? 'pending' }}">
                                {{ ucfirst($application->status ?? 'pending') }}
                            </span>
                        </div>
                        <div class="activity-time">
                            {{ $application->created_at->format('M d, H:i') }}
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-inbox text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No recent applications</p>
                    </div>
                @endif
            </div>
        </div>

        <div class="col-xl-4 col-lg-12 col-md-12 col-sm-12 mb-4">
            <div class="activity-card">
                <h5><i class="fas fa-sign-in-alt text-success me-2"></i>Today's Logins</h5>
                @if($recentLogins->count() > 0)
                    @foreach($recentLogins as $login)
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user text-success"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-title">{{ $login->user->name }}</p>
                            <p class="activity-subtitle">{{ $login->company->name }}</p>
                            <span class="status-badge active">{{ ucfirst($login->rate_type) }} Rate</span>
                        </div>
                        <div class="activity-time">
                            {{ $login->login_time->format('H:i') }}
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-user-clock text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No logins today</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="activity-card">
                <h5><i class="fas fa-bolt text-warning me-2"></i>Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('users.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('companies.index') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-building me-2"></i>Manage Companies
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('shifts.index') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-calendar-alt me-2"></i>Manage Shifts
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ route('applications.preview') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-alt me-2"></i>Review Applications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects and animations
    const statCards = document.querySelectorAll('.stat-card');

    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
        // You can implement AJAX refresh here if needed
        console.log('Dashboard data refresh check...');
    }, 300000); // 5 minutes

    // Add loading states for quick action buttons
    const quickActionBtns = document.querySelectorAll('.btn');

    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';

                // Reset after navigation (this won't execute if page changes)
                setTimeout(() => {
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                }, 2000);
            }
        });
    });

    // Add real-time clock
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();

        // You can add a clock element to the header if needed
        console.log(`Current time: ${timeString} - ${dateString}`);
    }

    // Update clock every second
    setInterval(updateClock, 1000);

    // Add smooth scrolling for any anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add tooltips for stat cards (if Bootstrap tooltips are available)
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Add number animation for stat cards
    function animateNumbers() {
        const numbers = document.querySelectorAll('.stat-number');

        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent);
            const duration = 2000; // 2 seconds
            const increment = finalValue / (duration / 16); // 60fps
            let currentValue = 0;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                number.textContent = Math.floor(currentValue);
            }, 16);
        });
    }

    // Trigger number animation on page load
    setTimeout(animateNumbers, 500);
});
</script>

<style>
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.stat-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-item {
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
    border-radius: 8px;
}

/* Add pulse animation for active status badges */
.status-badge.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Responsive improvements */
@media (max-width: 576px) {
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }

    .stat-card {
        text-align: center;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
    }

    .activity-icon {
        margin: 0 0 0.5rem 0;
    }

    .activity-time {
        margin-top: 0.5rem;
    }
}
</style>
@endpush