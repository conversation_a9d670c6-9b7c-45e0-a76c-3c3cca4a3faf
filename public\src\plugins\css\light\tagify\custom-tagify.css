/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.tagify {
  background: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
}
.tagify:hover {
  border: 1px solid #bfc9d4;
}
.tagify.tagify--focus {
  border: 1px solid #3b3f5c;
}

.tagify__tag > div {
  background: #e0e6ed;
  color: #3b3f5c;
  padding: 9px 14px !important;
  border-radius: 11px;
}

.tagify__input {
  padding: 9px 20px !important;
}

.tagify__tag > div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #e0e6ed inset;
  background: #e0e6ed;
}
.tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div::before {
  box-shadow: none !important;
}

.tagify__tag__removeBtn {
  color: #fff;
  background: #181e3a;
  font-size: 11px;
}
.tagify__tag__removeBtn:after {
  margin-left: 0.5px;
}
.tagify__tag__removeBtn:hover {
  background: #0e1726;
}

.tagify__tag:focus div::before, .tagify__tag:hover:not([readonly]) div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #e0e6ed inset;
}

.tagify__tag__removeBtn:hover + div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #e0e6ed inset !important;
}

.tagify__tag__avatar-wrap img {
  width: 16px;
  height: 16px;
  margin-right: 9px;
  border-radius: 6px;
}

.tagify__tag:hover .tagify__tag__avatar-wrap {
  transform: scale(1.6) translateX(-10%);
}

.tagify__input::before {
  color: #bfc9d4;
  margin-top: 3px;
}

.tagify__dropdown__wrapper {
  background: #fff;
  border: 1px solid #e0e6ed;
  box-shadow: 0 0.1px 0px rgba(0, 0, 0, 0), 0 0.2px 0px rgba(0, 0, 0, 0), 0 0.4px 0px rgba(0, 0, 0, 0), 0 0.6px 0px rgba(0, 0, 0, 0), 0 0.9px 0px rgba(0, 0, 0, 0.01), 0 1.2px 0px rgba(0, 0, 0, 0.01), 0 1.8px 0px rgba(0, 0, 0, 0.01), 0 2.6px 0px rgba(0, 0, 0, 0.01), 0 3.9px 0px rgba(0, 0, 0, 0.01), 0 7px 0px rgba(0, 0, 0, 0.01);
}

.tagify__input:focus:empty::before {
  color: #bfc9d4;
}

/* Suggestions items */
.tagify__dropdown.users-list .tagify__dropdown__item {
  padding: 0.5em 0.7em;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0 1em;
  grid-template-areas: "avatar name" "avatar email";
}
.tagify__dropdown.users-list .tagify__dropdown__item:hover .tagify__dropdown__item__avatar-wrap {
  transform: scale(1.2);
}
.tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  grid-area: avatar;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background: #EEE;
  transition: 0.1s ease-out;
}
.tagify__dropdown.users-list img {
  width: 100%;
  vertical-align: top;
}
.tagify__dropdown.users-list strong {
  grid-area: name;
  width: 100%;
  align-self: center;
  color: #3b3f5c;
}
.tagify__dropdown.users-list span {
  grid-area: email;
  width: 100%;
  font-size: 0.9em;
  color: #888ea8;
}
.tagify__dropdown.users-list .addAll {
  border-bottom: 1px solid #e0e6ed;
  gap: 0;
}

.tagify__dropdown__item--active {
  background: #ebedf2;
  color: #3b3f5c;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
