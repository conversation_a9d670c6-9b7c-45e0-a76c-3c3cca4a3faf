<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StaffLogin;
use App\Models\Company;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Gate;


class AttendanceController extends Controller
{
    public function index(Request $request)
    {
        $query = StaffLogin::with('user', 'company')->orderByDesc('login_time');

        if ($request->filled('date')) {
            $query->whereDate('login_time', $request->date);
        }

        if ($request->filled('staff_id')) {
            $query->where('user_id', $request->staff_id);
        }

        if ($request->filled('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        $records = $query->paginate(20);

        return view('Admin.attendance.index', [
            'records' => $records,
            'staff' => User::role('Staff')->get(),
            'companies' => Company::all(),
            'filters' => $request->only(['date', 'staff_id', 'company_id']),
        ]);
    }

    public function edit(StaffLogin $staffLogin)
    {
        Gate::authorize('edit-attendance', $staffLogin);
        return view('Admin.attendance.edit', compact('staffLogin'));
    }

    public function update(Request $request, StaffLogin $staffLogin)
    {
        Gate::authorize('edit-attendance', $staffLogin);

        $validated = $request->validate([
            'login_time' => 'required|date',
            'logout_time' => 'nullable|date|after:login_time',
            'rate_type' => 'required|in:regular,double,triple',
        ]);

        $staffLogin->update($validated);

        return redirect()->route('attendance.index')->with('success', 'Attendance updated.');
    }

    public function export(Request $request)
    {
        // Placeholder: implement Excel export later
        return back()->with('info', 'Export coming soon.');
    }
}
