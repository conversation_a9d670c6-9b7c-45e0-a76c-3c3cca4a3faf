@extends('layouts.app')

@push('styles')
    {{-- Select2 CSS --}}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    {{-- Custom Fix for Cork Conflicts --}}
    <style>
        .select2-container--default .select2-selection--multiple {
            background-color: #fff;
            border: 1px solid #d3d3d3;
            border-radius: 0.25rem;
            padding: 0.375rem 0.5rem;
            min-height: 38px;

        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #3b3f5c;
            color: #fff;
            border: none;
            padding: 0.25rem 0.5rem;
            margin-top: 4px;
            margin-right: 4px;
            border-radius: 4px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered {
            display: flex;
            flex-wrap: wrap;
            margin: auto;
        }

        .select2-container--default .select2-selection--multiple .select2-search__field {
            padding: 0.25rem;
            /* margin: auto; */
        }
    </style>
@endpush

@section('content')
    <div class="container mt-4">
        <h3>Create User</h3>
        <form action="{{ route('users.store') }}" method="POST">
            @csrf

            <div class="mb-3">
                <label>Name</label>
                <input name="name" class="form-control" required>
            </div>

            <div class="mb-3">
                <label>Email</label>
                <input name="email" type="email" class="form-control" required>
            </div>

            <div class="mb-3">
                <label>Password</label>
                <input name="password" type="password" class="form-control" required>
            </div>

            <div class="mb-3">
                <label>Confirm Password</label>
                <input name="password_confirmation" type="password" class="form-control" required>
            </div>

            <div class="mb-3">
                <label>Assign Companies</label>
                <select class="form-control select2" name="company_ids[]" multiple="multiple"
                    data-placeholder="Select Companies"required>
                    @foreach ($companies as $company)
                        <option value="{{ $company->id }}">{{ $company->name }}</option>
                    @endforeach
                </select>
            </div>

            <div class="mb-3">
                <label for="hourly_rate" class="form-label">Hourly Rate</label>
                <input name="hourly_rate" id="hourly_rate" type="number" step="0.01" class="form-control">
            </div>

            <div class="mb-3">
                <label>Assign Role</label>
                <select name="role" class="form-control" required>
                    @foreach ($roles as $role)
                        <option value="{{ $role->name }}">{{ ucfirst($role->name) }}</option>
                    @endforeach
                </select>
            </div>

            <button class="btn btn-success">Create User</button>
        </form>
    </div>
@endsection

@push('scripts')
    {{-- jQuery --}}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    {{-- Select2 JS --}}
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            $('.select2').select2({
                width: 'resolve',
                placeholder: $(this).data('placeholder'),
                allowClear: true
            });
        });
    </script>
@endpush
