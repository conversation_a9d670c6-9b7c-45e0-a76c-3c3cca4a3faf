<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\Shift;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use App\Notifications\MissedShiftAlert;


class CheckMissedShifts implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $threshold = now()->subMinutes(10);

        $shifts = Shift::with('user')
            ->where('start_time', '<=', $threshold)
            ->whereDoesntHave('staffLogin', function ($query) {
                $query->where('login_time', '>=', DB::raw('shifts.start_time'));
            })->get();

        foreach ($shifts as $shift) {
            // Alert the admin or team leader
            Notification::route('mail', '<EMAIL>')
                ->notify(new MissedShiftAlert($shift));
        }
    }
}
