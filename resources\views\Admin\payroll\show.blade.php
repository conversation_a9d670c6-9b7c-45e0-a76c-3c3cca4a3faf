@extends('layouts.app')

@section('content')
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <a href="{{ route('staff.timesheet') }}" class="btn btn-secondary mb-2">← Back to Stafs</a>
        <div class="d-flex gap-2">
            <a href="{{ route('payroll.export.excel', ['userId' => $userId, 'from' => $from->toDateString(), 'to' => $to->toDateString()]) }}"
                class="btn btn-success">Export Excel</a>
            <a href="{{ route('payroll.export.pdf', ['userId' => $userId, 'from' => $from->toDateString(), 'to' => $to->toDateString()]) }}"
                class="btn btn-danger">Export PDF</a>
        </div>
    </div>

    {{-- Filter Form --}}
    <form method="GET" class="row mb-4">
        <div class="col-md-3">
            <label for="from">From</label>
            <input type="date" name="from" id="from" class="form-control"
                value="{{ request('from', $from->toDateString()) }}">
        </div>
        <div class="col-md-3">
            <label for="to">To</label>
            <input type="date" name="to" id="to" class="form-control"
                value="{{ request('to', $to->toDateString()) }}">
        </div>
        <div class="col-md-3 align-self-end">
            <button type="submit" class="btn btn-primary">Filter</button>
        </div>
    </form>

    <div class="row layout-top-spacing">
        <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
            <div class="widget-content widget-content-area br-8">

                <table id="zero-config" class="table dt-table-hover" style="width:100%">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Company</th>
                            <th>Regular Hours</th>
                            <th>Double Hours</th>
                            <th>Triple Hours</th>
                            <th>Total Hours</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($hourSummary as $index => $row)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>
                                    @php
                                        $company = \App\Models\Company::find($row['company_id']);
                                    @endphp
                                    <span class="badge bg-info">{{ $company->name ?? 'Unknown' }}</span>
                                </td>
                                <td>{{ number_format($row['regular'], 2) }}</td>
                                <td>{{ number_format($row['double'], 2) }}</td>
                                <td>{{ number_format($row['triple'], 2) }}</td>
                                <td>
                                    <strong>
                                        {{ number_format($row['regular'] + $row['double'] + $row['triple'], 2) }}
                                    </strong>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">No records found for this user.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="../src/plugins/src/table/datatable/datatables.js"></script>
    <script>
        $('#zero-config').DataTable({
            "dom": "<'dt--top-section'<'row'<'col-12 col-sm-6 d-flex justify-content-sm-start justify-content-center'l><'col-12 col-sm-6 d-flex justify-content-sm-end justify-content-center mt-sm-0 mt-3'f>>>" +
                "<'table-responsive'tr>" +
                "<'dt--bottom-section d-sm-flex justify-content-sm-between text-center'<'dt--pages-count  mb-sm-0 mb-3'i><'dt--pagination'p>>",
            "oLanguage": {
                "oPaginate": {
                    "sPrevious": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line>
                        <polyline points="12 19 5 12 12 5"></polyline></svg>`,
                    "sNext": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line>
                        <polyline points="12 5 19 12 12 19"></polyline></svg>`
                },
                "sInfo": "Showing page _PAGE_ of _PAGES_",
                "sSearch": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-search"><circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>`,
                "sSearchPlaceholder": "Search...",
                "sLengthMenu": "Results :  _MENU_",
                "sZeroRecords": "No payroll records found."
            },
            "stripeClasses": [],
            "lengthMenu": [7, 10, 20, 50],
            "pageLength": 10
        });
    </script>
@endpush
