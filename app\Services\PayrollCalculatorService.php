<?php

namespace App\Services;

use App\Models\StaffLogin;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class PayrollCalculatorService
{
    public function calculateUserHours(int $userId, Carbon $from, Carbon $to): Collection
    {
        $logins = StaffLogin::where('user_id', $userId)
            ->whereBetween('login_time', [$from->startOfDay(), $to->endOfDay()])
            ->whereNotNull('logout_time')
            ->get();

        $grouped = $logins->groupBy('company_id');

        $result = collect();

        foreach ($grouped as $companyId => $entries) {
            $totals = [
                'regular' => 0,
                'double' => 0,
                'triple' => 0,
                'company_id' => $companyId,
            ];

            foreach ($entries as $entry) {

                $login = Carbon::parse($entry->login_time);
                $logout = Carbon::parse($entry->logout_time);

                // Calculate hours
                $hours = $logout->diffInMinutes($login) / 60;

                
                match ($entry->rate_type ?? 'regular') {
                    'double' => $totals['double'] += $hours,
                    'triple' => $totals['triple'] += $hours,
                    default  => $totals['regular'] += $hours,
                };
            }

            $result->push($totals);
        }

        return $result;
    }
}
