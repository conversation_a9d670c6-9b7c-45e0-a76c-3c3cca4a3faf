@extends('layouts.app')

@push('styles')
<style>
    .shift-detail-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .detail-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .card-header-custom {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }

    .info-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #E9A852;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
    }

    .info-label i {
        margin-right: 0.5rem;
        color: #E9A852;
    }

    .info-value {
        font-weight: 500;
        color: #212529;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-orange {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        border: none;
        color: white;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }

    .btn-orange:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(233, 168, 82, 0.3);
        color: white;
    }
</style>
@endpush

@section('content')
<div class="shift-detail-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="detail-card">
                    <div class="card-header-custom">
                        <h3 class="mb-2"><i class="fas fa-calendar-check me-2"></i>Shift Details</h3>
                        <p class="mb-0 opacity-75">Complete information about this shift assignment</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Staff Information -->
                        <div class="info-section">
                            <h5 class="mb-3 text-primary"><i class="fas fa-user me-2"></i>Staff Information</h5>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-user-circle"></i>Staff Member
                                </span>
                                <span class="info-value">{{ $shift->user->name }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-envelope"></i>Email
                                </span>
                                <span class="info-value">{{ $shift->user->email }}</span>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div class="info-section">
                            <h5 class="mb-3 text-primary"><i class="fas fa-building me-2"></i>Company Information</h5>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-building"></i>Company
                                </span>
                                <span class="info-value">
                                    <span class="badge bg-primary">{{ $shift->company->name }}</span>
                                </span>
                            </div>
                        </div>

                        <!-- Shift Details -->
                        <div class="info-section">
                            <h5 class="mb-3 text-primary"><i class="fas fa-clock me-2"></i>Shift Details</h5>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-play"></i>Start Time
                                </span>
                                <span class="info-value">{{ $shift->start_time->format('l, F j, Y \a\t g:i A') }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-stop"></i>End Time
                                </span>
                                <span class="info-value">{{ $shift->end_time->format('l, F j, Y \a\t g:i A') }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-hourglass-half"></i>Duration
                                </span>
                                <span class="info-value text-primary fw-bold">{{ $shift->formatted_duration }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-tags"></i>Shift Type
                                </span>
                                <span class="info-value">
                                    <span class="badge bg-{{ $shift->shift_type === 'regular' ? 'success' : ($shift->shift_type === 'overtime' ? 'warning' : 'info') }}">
                                        {{ ucfirst($shift->shift_type) }}
                                    </span>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-pound-sign"></i>Hourly Rate
                                </span>
                                <span class="info-value text-success fw-bold">£{{ number_format($shift->hourly_rate, 2) }}/hour</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-info-circle"></i>Status
                                </span>
                                <span class="info-value">
                                    <span class="status-badge bg-{{ $shift->status === 'active' ? 'success' : ($shift->status === 'scheduled' ? 'primary' : ($shift->status === 'completed' ? 'secondary' : 'danger')) }}">
                                        {{ ucfirst($shift->status) }}
                                    </span>
                                </span>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="info-section">
                            <h5 class="mb-3 text-primary"><i class="fas fa-info me-2"></i>Additional Information</h5>
                            @if($shift->notes)
                                <div class="info-item">
                                    <span class="info-label">
                                        <i class="fas fa-sticky-note"></i>Notes
                                    </span>
                                    <span class="info-value">{{ $shift->notes }}</span>
                                </div>
                            @endif
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-bell"></i>Reminder Sent
                                </span>
                                <span class="info-value">
                                    @if($shift->reminder_sent)
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-warning">No</span>
                                    @endif
                                </span>
                            </div>
                            @if($shift->isRecurring())
                                <div class="info-item">
                                    <span class="info-label">
                                        <i class="fas fa-repeat"></i>Recurring Pattern
                                    </span>
                                    <span class="info-value">
                                        <span class="badge bg-info">{{ ucfirst($shift->recurring_pattern) }}</span>
                                        @if($shift->recurring_until)
                                            until {{ $shift->recurring_until->format('M j, Y') }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-user-cog"></i>Created By
                                </span>
                                <span class="info-value">
                                    @if($shift->createdBy)
                                        {{ $shift->createdBy->name }}
                                    @else
                                        System
                                    @endif
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-calendar-plus"></i>Created At
                                </span>
                                <span class="info-value">{{ $shift->created_at->format('M j, Y \a\t g:i A') }}</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center mt-4">
                            <a href="{{ route('shifts.edit', $shift->id) }}" class="btn btn-orange me-2">
                                <i class="fas fa-edit me-1"></i>Edit Shift
                            </a>
                            <a href="{{ route('shifts.index') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            @if(!$shift->reminder_sent && $shift->start_time > now())
                                <button type="button" class="btn btn-info me-2" onclick="sendReminder({{ $shift->id }})">
                                    <i class="fas fa-bell me-1"></i>Send Reminder
                                </button>
                            @endif
                            <form action="{{ route('shifts.destroy', $shift->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this shift?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendReminder(shiftId) {
    if (confirm('Send a reminder notification to the staff member?')) {
        // Implementation for sending individual reminder
        alert('Reminder sent successfully!');
    }
}
</script>
@endsection
