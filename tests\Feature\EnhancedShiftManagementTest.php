<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\StaffLogin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

class EnhancedShiftManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $staff;
    protected $company;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'Staff']);
        Role::create(['name' => 'Admin']);

        // Create a company
        $this->company = Company::factory()->create();

        // Create a staff user
        $this->staff = User::factory()->create();
        $this->staff->assignRole('Staff');
        $this->staff->companies()->attach($this->company->id, ['hourly_rate' => 15.00]);
    }

    /** @test */
    public function staff_can_start_a_new_shift()
    {
        $response = $this->actingAs($this->staff)
            ->postJson('/shift/start');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Shift started successfully!'
            ]);

        $this->assertDatabaseHas('staff_logins', [
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'shift_status' => 'active'
        ]);
    }

    /** @test */
    public function staff_cannot_start_multiple_shifts()
    {
        // Create an active shift
        StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now(),
            'shift_status' => 'active'
        ]);

        $response = $this->actingAs($this->staff)
            ->postJson('/shift/start');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'You already have an active shift running.'
            ]);
    }

    /** @test */
    public function staff_can_pause_active_shift()
    {
        // Create an active shift
        $shift = StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now(),
            'shift_status' => 'active'
        ]);

        $response = $this->actingAs($this->staff)
            ->postJson('/shift/pause', [
                'reason' => 'Lunch Break'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Shift paused successfully!'
            ]);

        $shift->refresh();
        $this->assertEquals('paused', $shift->shift_status);
        $this->assertEquals('Lunch Break', $shift->current_pause_reason);
        $this->assertNotNull($shift->current_pause_start);
    }

    /** @test */
    public function staff_can_resume_paused_shift()
    {
        // Create a paused shift
        $shift = StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now()->subHour(),
            'shift_status' => 'paused',
            'current_pause_reason' => 'Lunch Break',
            'current_pause_start' => now()->subMinutes(30),
            'total_pause_minutes' => 0
        ]);

        $response = $this->actingAs($this->staff)
            ->postJson('/shift/resume');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Shift resumed successfully!'
            ]);

        $shift->refresh();
        $this->assertEquals('active', $shift->shift_status);
        $this->assertNull($shift->current_pause_reason);
        $this->assertNull($shift->current_pause_start);
        $this->assertGreaterThan(0, $shift->total_pause_minutes);
    }

    /** @test */
    public function staff_can_end_active_shift()
    {
        // Create an active shift
        $shift = StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now()->subHours(8),
            'shift_status' => 'active',
            'total_pause_minutes' => 60 // 1 hour pause
        ]);

        $response = $this->actingAs($this->staff)
            ->postJson('/shift/end');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Shift ended successfully!'
            ]);

        $shift->refresh();
        $this->assertEquals('ended', $shift->shift_status);
        $this->assertNotNull($shift->logout_time);
        $this->assertEquals(7, round($shift->work_hours)); // 8 hours - 1 hour pause
    }

    /** @test */
    public function staff_can_get_shift_status()
    {
        // Create an active shift
        $shift = StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now()->subHour(),
            'shift_status' => 'active'
        ]);

        $response = $this->actingAs($this->staff)
            ->getJson('/shift/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'has_active_shift' => true,
                    'status' => 'active',
                    'shift_id' => $shift->id
                ]
            ]);
    }

    /** @test */
    public function staff_can_get_today_statistics()
    {
        // Create completed shifts for today
        StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => today()->addHours(8),
            'logout_time' => today()->addHours(16),
            'shift_status' => 'ended',
            'total_pause_minutes' => 60
        ]);

        $response = $this->actingAs($this->staff)
            ->getJson('/shift/today-stats');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'today_shifts' => 1,
                    'today_work_hours' => 7.0 // 8 hours - 1 hour pause
                ]
            ]);
    }

    /** @test */
    public function pause_periods_are_tracked_correctly()
    {
        // Create an active shift
        $shift = StaffLogin::create([
            'user_id' => $this->staff->id,
            'company_id' => $this->company->id,
            'login_time' => now()->subHours(2),
            'shift_status' => 'active'
        ]);

        // Pause the shift
        $this->actingAs($this->staff)
            ->postJson('/shift/pause', ['reason' => 'Lunch Break']);

        // Simulate 30 minutes pause
        $shift->refresh();
        $shift->update(['current_pause_start' => now()->subMinutes(30)]);

        // Resume the shift
        $this->actingAs($this->staff)
            ->postJson('/shift/resume');

        $shift->refresh();
        
        $this->assertCount(1, $shift->pause_periods);
        $this->assertEquals('Lunch Break', $shift->pause_periods[0]['reason']);
        $this->assertEquals(30, round($shift->pause_periods[0]['duration_minutes']));
        $this->assertEquals(30, round($shift->total_pause_minutes));
    }

    /** @test */
    public function enhanced_dashboard_displays_shift_management_interface()
    {
        $response = $this->actingAs($this->staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('shift-management-container');
        $response->assertSee('mainShiftBtn');
        $response->assertSee('handleMainShiftAction');
        $response->assertSee('SweetAlert2');
    }
}
