<?php

use App\Http\Controllers\Admin\ApplicationController;
use App\Http\Controllers\Admin\AttendanceController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\PayrollController;
use App\Http\Controllers\Admin\ShiftController;
use App\Http\Controllers\Admin\StaffController;
use App\Http\Controllers\Admin\TimeSheetController;

Route::get('/', function () {
    return redirect('/login');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('test', function () {
    return view('test');
});

Route::get('login', [AuthController::class, 'showLoginForm'])->name('login.form');
Route::get('register', [AuthController::class, 'showRegisterForm'])->name('register.form');
Route::post('login', [AuthController::class, 'login'])->name('login.custom');
Route::post('register', [AuthController::class, 'register'])->name('register.custom');
Route::post('logout', [AuthController::class, 'logout'])->name('logout');

// // Example dashboard route
// Route::get('/dashboard', function () {
//     return view('home');
// })->middleware(['auth', 'role:admin,team-lead,staff'])->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::resource('permissions', PermissionController::class);
    Route::resource('roles', RoleController::class);
    Route::resource('companies', CompanyController::class);
    Route::resource('users', UserController::class);
    Route::resource('shifts', ShiftController::class);
    Route::get('shifts/bulk/create', [ShiftController::class, 'bulkCreate'])->name('shifts.bulk-create');
    Route::post('shifts/bulk/store', [ShiftController::class, 'bulkStore'])->name('shifts.bulk-store');
    Route::post('shifts/send-reminders', [ShiftController::class, 'sendReminders'])->name('shifts.send-reminders');
    Route::get('payroll/{userId}', [PayrollController::class, 'showPayroll'])->name('payroll.show');
    Route::get('showStaff', [StaffController::class, 'timesheet'])->name('staff.timesheet');
    Route::get('staff/timetable', [StaffController::class, 'timetable'])->name('staff.timetable');
    Route::get('staff/timesheet/{login}/details', [StaffController::class, 'timesheetDetails'])->name('staff.timesheet.details');

    // Shift Management Routes
    Route::prefix('shift')->name('shift.')->group(function () {
        Route::post('start', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'startShift'])->name('start');
        Route::post('pause', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'pauseShift'])->name('pause');
        Route::post('resume', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'resumeShift'])->name('resume');
        Route::post('end', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'endShift'])->name('end');
        Route::get('status', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'getShiftStatus'])->name('status');
        Route::get('today-stats', [\App\Http\Controllers\Staff\ShiftManagementController::class, 'getTodayStats'])->name('today-stats');
    });
    Route::get('payroll/{userId}/export/excel', [PayrollController::class, 'exportExcel'])->name('payroll.export.excel');
    Route::get('payroll/{userId}/export/pdf', [PayrollController::class, 'exportPdf'])->name('payroll.export.pdf');
    Route::get('notification', [NotificationController::class, 'index'])->name('notification.unread');
    Route::patch('notifications/{id}/read', [NotificationController::class, 'markAsRead'])
        ->name('notifications.markAsRead');

    // Route::get('applications', [ApplicationController::class, 'index'])->name('applications.index');
    // Route::get('applications/create', [ApplicationController::class, 'create'])->name('applications.create');
    // Route::post('applications', [ApplicationController::class, 'store'])->name('applications.store');

    //admin will review application and change status
    Route::get('preview/applications', [ApplicationController::class, 'adminIndex'])->name('applications.preview');
    Route::put('applications/{id}/status', [ApplicationController::class, 'updateStatus'])->name('applications.updateStatus');
    //timeshhet
    Route::get('timesheet', [TimeSheetController::class, 'staffTimeSheet'])->name('timesheet.staff.dashboard');
});

Route::middleware(['auth'])->prefix('dashboard')->group(function () {
    Route::get('/', [UserController::class, 'dashboard'])->name('dashboard');
});

// Route::middleware(['auth', 'role:Staff'])->group(function () {
Route::get('applications/create', [ApplicationController::class, 'create'])->name('applications.create');
Route::post('applications', [ApplicationController::class, 'store'])->name('applications.store');
Route::get('applications', [ApplicationController::class, 'index'])->name('applications.index');

// Route::get('timesheet', [TimeSheetController::class, 'dashboard'])->name('timesheet.staff.dashboard');
// });

Route::middleware(['auth'])->prefix('attendance')->name('attendance.')->group(function () {
    Route::get('/', [AttendanceController::class, 'index'])->name('index');
    // Route::get('/export', [AttendanceController::class, 'export'])->name('export'); // optional
    Route::get('/{staffLogin}/edit', [AttendanceController::class, 'edit'])->name('edit');
    Route::put('/{staffLogin}', [AttendanceController::class, 'update'])->name('update');
});
