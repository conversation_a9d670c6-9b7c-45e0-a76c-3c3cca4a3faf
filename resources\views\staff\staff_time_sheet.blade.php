@extends('layouts.app')

@section('content')
    <div class="container-fluid">
        <h4>Hello {{ Auth::user()->name }}!</h4>

        {{-- Time Record Table --}}
        <div class="card mb-4">
            <div class="card-header">Time Record Table</div>
            <div class="card-body table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Clock In</th>
                            <th>Clock Out</th>
                            <th>Total Hour</th>
                            <th>Shift Hour</th>
                            <th>Status</th>
                            <th>Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($logins as $login)
                        {{-- @dd($login); --}}
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($login->login_time)->format('F d, Y') }}</td>
                                <td>{{ optional($login->login_time)->format('g:i A') ?? '--' }}</td>
                                <td>{{ optional($login->logout_time)->format('g:i A') ?? '--' }}</td>
                                <td>
                                    @if ($login->login_time && $login->logout_time)
                                        @php
                                            $diff = \Carbon\Carbon::parse($login->logout_time)->diff(\Carbon\Carbon::parse($login->login_time));
                                            $totalMinutes = $diff->h * 60 + $diff->i;
                                            $hours = floor($totalMinutes / 60);
                                            $minutes = $totalMinutes % 60;
                                        @endphp
                                        {{ sprintf('%02d:%02d', $hours, $minutes) }}
                                    @else
                                        --
                                    @endif
                                </td>
                                <td>
                                    @if ($login->shift)
                                        @php
                                            $diff = \Carbon\Carbon::parse($login->shift->end_time)->diff(\Carbon\Carbon::parse($login->shift->start_time));
                                            $totalMinutes = $diff->h * 60 + $diff->i;
                                            $hours = floor($totalMinutes / 60);
                                            $minutes = $totalMinutes % 60;
                                        @endphp
                                        {{ sprintf('%02d:%02d', $hours, $minutes) }}
                                    @else
                                        --
                                    @endif
                                </td>
                                <td>
                                    <span
                                        class="text-{{ match ($login->status) {
                                            'on_time' => 'success',
                                            'late_in' => 'danger',
                                            'left_early' => 'warning',
                                            'absent' => 'secondary',
                                            default => 'muted',
                                        } }}">{{ ucfirst($login->status ?? 'Absent') }}</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showDetails({{ $login->id }})">
                                        <i class="fas fa-eye"></i> Details
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        {{-- Attendance Summary & Chart --}}
        <div class="row">
            <div class="col-md-4">
                <div class="card p-3">
                    <h5>{{ round(($present / max($total, 1)) * 100) }}% Active Attendance</h5>
                    <p><strong>{{ $present }}</strong> Present • <strong>{{ $leave }}</strong> Leave •
                        <strong>{{ $absent }}</strong> Absent</p>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card p-3">
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Details Modal -->
        <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="detailsModalLabel">Shift Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="detailsContent">
                        <!-- Content will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {!! json_encode($monthlyWorked->keys()) !!},
                datasets: [{
                    label: 'Worked Minutes',
                    data: {!! json_encode($monthlyWorked->values()) !!},
                    backgroundColor: '#4caf50'
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: val => (val / 60).toFixed(1) + ' hr'
                        }
                    }
                }
            }
        });

        // Details modal functionality
        async function showDetails(loginId) {
            try {
                const response = await fetch(`/staff/timesheet/${loginId}/details`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    const content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Basic Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Date:</strong></td><td>${data.date}</td></tr>
                                    <tr><td><strong>Company:</strong></td><td>${data.company}</td></tr>
                                    <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(data.status)}">${data.status}</span></td></tr>
                                    <tr><td><strong>Rate Type:</strong></td><td>${data.rate_type}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Time Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Clock In:</strong></td><td>${data.login_time}</td></tr>
                                    <tr><td><strong>Clock Out:</strong></td><td>${data.logout_time}</td></tr>
                                    <tr><td><strong>Total Work:</strong></td><td>${data.total_work_hours}</td></tr>
                                    <tr><td><strong>Total Breaks:</strong></td><td>${data.total_pause_minutes}</td></tr>
                                </table>
                            </div>
                        </div>
                        ${data.pause_periods && data.pause_periods.length > 0 ? `
                            <div class="mt-3">
                                <h6 class="text-primary">Break History</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Reason</th>
                                                <th>Start Time</th>
                                                <th>End Time</th>
                                                <th>Duration</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.pause_periods.map(period => `
                                                <tr>
                                                    <td>${period.reason}</td>
                                                    <td>${new Date(period.start).toLocaleTimeString()}</td>
                                                    <td>${new Date(period.end).toLocaleTimeString()}</td>
                                                    <td>${period.duration_minutes} min</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        ` : ''}
                        ${data.is_active ? '<div class="alert alert-info mt-3"><i class="fas fa-clock"></i> This shift is currently active.</div>' : ''}
                    `;

                    document.getElementById('detailsContent').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                } else {
                    alert('Failed to load details');
                }
            } catch (error) {
                console.error('Error loading details:', error);
                alert('Failed to load details');
            }
        }

        function getStatusColor(status) {
            switch(status.toLowerCase()) {
                case 'present':
                case 'on_time': return 'success';
                case 'late_in': return 'warning';
                case 'left_early': return 'info';
                case 'absent': return 'danger';
                default: return 'secondary';
            }
        }
    </script>
@endpush
