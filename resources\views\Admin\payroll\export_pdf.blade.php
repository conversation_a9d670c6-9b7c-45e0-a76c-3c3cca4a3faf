<!DOCTYPE html>
<html>
<head>
    <title>Payroll PDF</title>
    <style>
        table { width: 100%; border-collapse: collapse; font-size: 14px; }
        th, td { border: 1px solid #000; padding: 5px; text-align: center; }
        th { background-color: #f0f0f0; }
    </style>
</head>
<body>
    <h3>Payroll Report</h3>
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>Company</th>
                <th>Regular Hours</th>
                <th>Double Hours</th>
                <th>Triple Hours</th>
                <th>Total Hours</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($hourSummary as $index => $row)
                @php $company = \App\Models\Company::find($row['company_id']); @endphp
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $company->name ?? 'Unknown' }}</td>
                    <td>{{ number_format($row['regular'], 2) }}</td>
                    <td>{{ number_format($row['double'], 2) }}</td>
                    <td>{{ number_format($row['triple'], 2) }}</td>
                    <td>{{ number_format($row['regular'] + $row['double'] + $row['triple'], 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</body>
</html>
