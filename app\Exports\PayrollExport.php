<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class PayrollExport implements FromCollection
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        //
    }
    public $hourSummary;

    public function __construct($hourSummary)
    {
        $this->hourSummary = $hourSummary;
    }

    public function view(): View
    {
        return view('admin.payroll.export_excel', [
            'hourSummary' => $this->hourSummary
        ]);
    }

}
