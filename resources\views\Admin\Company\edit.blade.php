@extends('layouts.app')

@section('content')
    <form method="POST" action="{{ route('companies.update', $company->id) }}">
        @csrf
        @method('PUT')

        <div class="mb-3">
            <label>Company Name</label>
            <input type="text" name="name" value="{{ $company->name }}" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>Email Address</label>
            <input type="email" name="email_address" value="{{ $company->email_address }}" class="form-control" required>
        </div>
        {{-- <div class="mb-3">
        <label>Timezone</label>
        <input type="text" name="timezone" value="{{ $company->timezone }}" class="form-control" required>
    </div> --}}
        <div class="mb-3">
            <label>Address</label>
            <input type="text" name="address" value="{{ $company->address }}" class="form-control">
        </div>
        <div class="mb-3">
            <label>Phone Number</label>
            <input type="text" name="phone_number" value="{{ $company->phone_number }}" class="form-control">
        </div>
        <div class="mb-3">
            <label>Status</label>
            <select name="status" class="form-control" required>
                <option value="1" {{ $company->status == 1 ? 'selected' : '' }}>Active</option>
                <option value="0" {{ $company->status == 0 ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>

        {{-- <div class="mb-3">
        <label>Assign Users</label>
        <select name="users[]" class="form-control" multiple>
            @foreach ($users as $user)
                <option value="{{ $user->id }}" {{ in_array($user->id, $company->users->pluck('id')->toArray()) ? 'selected' : '' }}>
                    {{ $user->name }} ({{ $user->email }})
                </option>
            @endforeach
        </select>
    </div> --}}
        <button type="submit" class="btn btn-success">Update Company</button>
    </form>
@endsection
