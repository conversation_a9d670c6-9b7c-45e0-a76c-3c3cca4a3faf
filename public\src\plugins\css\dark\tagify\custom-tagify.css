/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .tagify {
  background: #1b2e4b;
  border: 1px solid #1b2e4b;
  border-radius: 6px;
}
body.dark .tagify:hover {
  border: 1px solid #1b2e4b;
}
body.dark .tagify.tagify--focus {
  border: 1px solid #3b3f5c;
}
body.dark .tagify__tag > div {
  background: #060818;
  color: #fff;
  padding: 9px 14px;
  border-radius: 11px;
}
body.dark .tagify__input {
  padding: 9px 20px;
}
body.dark .tagify__tag > div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #060818 inset;
  background: #060818;
}
body.dark .tagify__tag.tagify--notAllowed:not(.tagify__tag--editable) div::before {
  box-shadow: none !important;
}
body.dark .tagify__tag__removeBtn {
  color: #fff;
  background: #181e3a;
  font-size: 11px;
}
body.dark .tagify__tag__removeBtn:after {
  margin-left: 0.5px;
}
body.dark .tagify__tag__removeBtn:hover {
  background: #0e1726;
}
body.dark .tagify__tag:focus div::before, body.dark .tagify__tag:hover:not([readonly]) div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #060818 inset;
}
body.dark .tagify__tag__removeBtn:hover + div::before {
  box-shadow: 0 0 0 var(--tag-inset-shadow-size, 1.1em) #060818 inset !important;
}
body.dark .tagify__tag__avatar-wrap img {
  width: 16px;
  height: 16px;
  margin-right: 9px;
  border-radius: 6px;
}
body.dark .tagify__tag:hover .tagify__tag__avatar-wrap {
  transform: scale(1.6) translateX(-10%);
}
body.dark .tagify__input::before {
  color: #bfc9d4;
  margin-top: 3px;
}
body.dark .tagify__dropdown__wrapper {
  background: #0e1726;
  border: 1px solid #3b3f5c;
  box-shadow: 0 0.1px 0px rgba(0, 0, 0, 0), 0 0.2px 0px rgba(0, 0, 0, 0), 0 0.4px 0px rgba(0, 0, 0, 0), 0 0.6px 0px rgba(0, 0, 0, 0), 0 0.9px 0px rgba(0, 0, 0, 0.01), 0 1.2px 0px rgba(0, 0, 0, 0.01), 0 1.8px 0px rgba(0, 0, 0, 0.01), 0 2.6px 0px rgba(0, 0, 0, 0.01), 0 3.9px 0px rgba(0, 0, 0, 0.01), 0 7px 0px rgba(0, 0, 0, 0.01);
}
body.dark .tagify__input:focus:empty::before {
  color: #bfc9d4;
}

/* Suggestions items */
body.dark .tagify__dropdown.users-list .tagify__dropdown__item {
  padding: 0.5em 0.7em;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0 1em;
  grid-template-areas: "avatar name" "avatar email";
}
body.dark .tagify__dropdown.users-list .tagify__dropdown__item:hover .tagify__dropdown__item__avatar-wrap {
  transform: scale(1.2);
}
body.dark .tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  grid-area: avatar;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background: #EEE;
  transition: 0.1s ease-out;
}
body.dark .tagify__dropdown.users-list img {
  width: 100%;
  vertical-align: top;
}
body.dark .tagify__dropdown.users-list strong {
  grid-area: name;
  width: 100%;
  align-self: center;
  color: #f1f2f3;
}
body.dark .tagify__dropdown.users-list span {
  grid-area: email;
  width: 100%;
  font-size: 0.9em;
  color: #bfc9d4;
}
body.dark .tagify__dropdown.users-list .addAll {
  border-bottom: 1px solid #3b3f5c;
  gap: 0;
}
body.dark .tagify__dropdown__item--active {
  background: #060818;
  color: #888ea8;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
