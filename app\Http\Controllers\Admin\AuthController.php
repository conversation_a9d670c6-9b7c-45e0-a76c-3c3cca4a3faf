<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Company;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use App\Models\StaffLogin;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        $companies = Company::where('status', true)->get();
        return view('auth.login', compact('companies'));
    }
    public function showRegisterForm()
    {
        $companies = Company::where('status', true)->get();
        $roles = Role::all();
        return view('auth.register', compact('companies', 'roles'));
    }

    public function register(Request $request)
    {
        // dd($request->all());
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'confirmed', 'min:6'],
            'company_id' => ['required', 'array', 'min:1'],
            'company_id.*' => ['exists:companies,id'],
            'role_id' => 'required|exists:roles,id',
        ]);
        // dd($validated);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
        ]);
        $user->companies()->sync($validated['company_id']);
        $user->assignRole(Role::find($validated['role_id'])->name);
        Auth::login($user);

        return redirect()->route('home')->with('success', 'Account created successfully.');
    }



    // public function login(Request $request)
    // {
    //     $request->validate([
    //         'email'        => 'required|email',
    //         'password'     => 'required',
    //         'company_id'   => 'required|array|min:1',
    //         'company_id.*' => 'exists:companies,id',
    //     ]);

    //     $user = User::where('email', $request->email)->first();

    //     if (!$user || !Hash::check($request->password, $user->password)) {
    //         return back()->withErrors(['email' => 'Invalid credentials.']);
    //     }

    //     // Check for existing open staff logins in other companies (only for Staff)
    //     if ($user->hasRole('Staff')) {
    //         $openLogins = StaffLogin::where('user_id', $user->id)
    //             ->whereNull('logout_time')
    //             ->pluck('company_id')
    //             ->toArray();

    //         $conflicts = array_diff($request->company_id, $openLogins);

    //         if (count($openLogins) && count($conflicts)) {
    //             return back()->withErrors([
    //                 'company_id' => 'You already have an active login session in another company. Please logout first.'
    //             ]);
    //         }
    //     }

    //     Auth::login($user);

    //     // Store selected companies in session
    //     session(['selected_company_ids' => $request->company_id]);

    //     // Sync selected companies (without removing existing ones)
    //     foreach ($request->company_id as $companyId) {
    //         $user->companies()->syncWithoutDetaching([$companyId]);
    //     }

    //     // Determine rate type
    //     $rateType = match (count($request->company_id)) {
    //         2           => 'double',
    //         3, 4, 5     => 'triple',
    //         default     => 'regular',
    //     };

    //     // Track login only if Staff
    //     if ($user->hasRole('Staff')) {
    //         foreach ($request->company_id as $companyId) {
    //             $existing = StaffLogin::where('user_id', $user->id)
    //                 ->where('company_id', $companyId)
    //                 ->whereNull('logout_time')
    //                 ->first();

    //             if (!$existing) {
    //                 StaffLogin::create([
    //                     'user_id'    => $user->id,
    //                     'company_id' => $companyId,
    //                     'login_time' => now(),
    //                     'rate_type'  => $rateType,
    //                 ]);
    //             }
    //         }
    //     }

    //     return redirect()->route('home');
    // }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt(['email' => $request->email, 'password' => $request->password])) {
            return redirect()->route('dashboard')->with('success', 'Login successfully.');
        } else {
            return back()->withErrors(['email' => 'Invalid credentials.']);
        }

        // $user = User::where('email', $request->email)->first();

        // if (!$user || !Hash::check($request->password, $user->password)) {
        //     return back()->withErrors(['email' => 'Invalid credentials.']);
        // }

        // //Make sure user has selected role
        // if (!$user->hasRole($request->role)) {
        //     return back()->withErrors(['role' => 'You do not have this role.']);
        // }

        //open logins only for Staff
        // if ($request->role === 'Staff') {
        //     $openLogins = StaffLogin::where('user_id', $user->id)
        //         ->whereNull('logout_time')
        //         ->pluck('company_id')
        //         ->toArray();

        //     $conflicts = array_diff($request->company_id ?? [], $openLogins);

        //     if (count($openLogins) && count($conflicts)) {
        //         return back()->withErrors([
        //             'company_id' => 'You already have an active login session in another company. Please logout first.'
        //         ]);
        //     }
        // }

        // Log the user in
        // Auth::login($user);

        // Company handling only for non-admins
        // if ($request->role !== 'Admin') {
        //     session(['selected_company_ids' => $request->company_id]);

        //     foreach ($request->company_id as $companyId) {
        //         $user->companies()->syncWithoutDetaching([$companyId]);
        //     }

        //     $rateType = match (count($request->company_id)) {
        //         2           => 'double',
        //         3, 4, 5     => 'triple',
        //         default     => 'regular',
        //     };

        //     if ($request->role === 'Staff') {
        //         foreach ($request->company_id as $companyId) {
        //             $existing = StaffLogin::where('user_id', $user->id)
        //                 ->where('company_id', $companyId)
        //                 ->whereNull('logout_time')
        //                 ->first();

        //             if (!$existing) {
        //                 StaffLogin::create([
        //                     'user_id'    => $user->id,
        //                     'company_id' => $companyId,
        //                     'login_time' => now(),
        //                     'rate_type'  => $rateType,
        //                 ]);
        //             }
        //         }
        //     }
        // }

        return redirect()->route('home')->with('success', 'Login successfully.');
    }




    public function logout()
    {
        if (Auth::check()) {
            $user = Auth::user();
            $companyIds = session('selected_company_ids', []);

            // Only track staff logout
            if ($user->hasRole('Staff')) {
                foreach ($companyIds as $companyId) {
                    StaffLogin::where('user_id', $user->id)
                        ->where('company_id', $companyId)
                        ->whereNull('logout_time')
                        ->update(['logout_time' => now()]);
                }
            }
        }
        Auth::logout();
        session()->flush();

        return redirect()->route('login.form');
    }
}
