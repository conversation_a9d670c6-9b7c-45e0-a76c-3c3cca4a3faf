<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            if (!Schema::hasColumn('staff_logins', 'status')) {
                $table->string('status')->default('on_time')->after('shift_status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('staff_logins', function (Blueprint $table) {
            if (Schema::hasColumn('staff_logins', 'status')) {
                $table->dropColumn('status');
            }
        });
    }
};
