/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.mt-container {
  max-width: 570px;
}

.modern-timeline {
  list-style: none;
  position: relative;
  padding: 50px 0 50px;
  margin: 0;
}
.modern-timeline:before {
  position: absolute;
  background: #ebedf2;
  bottom: 0;
  left: 50%;
  top: 0;
  content: "";
  width: 3px;
  margin-left: -1.5px;
}
.modern-timeline > li {
  margin-bottom: 50px;
  position: relative;
}
.modern-timeline > li:after, .modern-timeline > li:before {
  display: table;
  content: "";
}
.modern-timeline > li > .modern-timeline-badge {
  position: absolute;
  background: #fff;
  border: 3px solid #ebedf2;
  border-radius: 100%;
  height: 20px;
  width: 20px;
  margin-left: -10px;
  text-align: center;
  z-index: 1;
  left: 50%;
  top: 32px;
}
.modern-timeline > li > .modern-timeline-panel {
  position: relative;
  border: 1px solid #e0e6ed;
  background: #fff;
  border-radius: 0.1875rem;
  transition: 0.3s ease-in-out;
  float: left;
  width: 46%;
  border-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel:before {
  position: absolute;
  background: #ebedf2;
  right: -37px;
  top: 40px;
  transition: 0.3s ease-in-out;
  content: " ";
  width: 37px;
  height: 3px;
  display: block;
}
.modern-timeline > li:nth-child(even) > .modern-timeline-panel:before {
  right: auto;
  left: -37px;
  width: 37px;
}
.modern-timeline > li:after {
  clear: both;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-preview img {
  width: 100%;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}
.modern-timeline > li:nth-child(even) > .modern-timeline-panel {
  border: 1px solid #e0e6ed;
  float: right;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body {
  padding: 30px 20px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body h4 {
  color: #e95f2b;
  margin-bottom: 20px;
  font-size: 1.125rem;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p {
  color: #888ea8;
  margin-bottom: 0;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p a {
  display: block;
}
.modern-timeline > li > .modern-timeline-panel *:last-child {
  margin-bottom: 0;
}

.modern-timeline-top:before, .modern-timeline-bottom:before {
  background: #ebedf2;
  position: absolute;
  height: 3px;
  width: 50px;
  display: block;
  content: "";
  bottom: 0;
  left: 50%;
  margin-left: -25px;
}

.modern-timeline-top:before {
  top: 0;
}

@media (max-width: 767px) {
  ul.modern-timeline > li > .modern-timeline-panel {
    border: 1px solid #e0e6ed;
    float: right;
    width: 100%;
  }
  ul.modern-timeline > li > .modern-timeline-badge {
    display: none;
  }
  .modern-timeline > li > .modern-timeline-panel:before {
    display: none;
  }
}
/*
=====================
    Basic
=====================
*/
.timeline-line .item-timeline {
  display: flex;
}
.timeline-line .item-timeline .t-dot {
  position: relative;
}
.timeline-line .item-timeline .t-dot:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  border-color: #2196f3;
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-line .item-timeline .t-dot.t-dot-primary:before {
  border-color: #4361ee;
}
.timeline-line .item-timeline .t-dot.t-dot-success:before {
  border-color: #00ab55;
}
.timeline-line .item-timeline .t-dot.t-dot-warning:before {
  border-color: #e2a03f;
}
.timeline-line .item-timeline .t-dot.t-dot-info:before {
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot.t-dot-danger:before {
  border-color: #e7515a;
}
.timeline-line .item-timeline .t-dot.t-dot-dark:before {
  border-color: #3b3f5c;
}
.timeline-line .item-timeline .t-dot.t-dot-primary:after {
  border-color: #4361ee;
}
.timeline-line .item-timeline .t-dot.t-dot-success:after {
  border-color: #00ab55;
}
.timeline-line .item-timeline .t-dot.t-dot-warning:after {
  border-color: #e2a03f;
}
.timeline-line .item-timeline .t-dot.t-dot-info:after {
  border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot.t-dot-danger:after {
  border-color: #e7515a;
}
.timeline-line .item-timeline .t-dot.t-dot-dark:after {
  border-color: #3b3f5c;
}
.timeline-line .item-timeline:last-child .t-dot:after {
  display: none;
}
.timeline-line .item-timeline .t-meta-time {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
  align-self: center;
}
.timeline-line .item-timeline .t-text {
  padding: 10px;
  align-self: center;
  margin-left: 10px;
}
.timeline-line .item-timeline .t-text p {
  font-size: 13px;
  margin: 0;
  color: #3b3f5c;
  font-weight: 600;
}
.timeline-line .item-timeline .t-text p a {
  color: #4361ee;
  font-weight: 600;
}
.timeline-line .item-timeline .t-time {
  margin: 0;
  min-width: 58px;
  max-width: 100px;
  font-size: 16px;
  font-weight: 600;
  color: #3b3f5c;
  padding: 10px 0;
}
.timeline-line .item-timeline .t-text .t-meta-time {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
  align-self: center;
}

/*
=====================
    Modern
=====================
*/
.timeline-alter .item-timeline {
  display: flex;
}
.timeline-alter .item-timeline .t-time {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-time p {
  margin: 0;
  min-width: 58px;
  max-width: 100px;
  font-size: 16px;
  font-weight: 600;
  color: #3b3f5c;
  align-self: center;
}
.timeline-alter .item-timeline .t-img {
  position: relative;
  border-color: #e0e6ed;
  padding: 10px;
}
.timeline-alter .item-timeline .t-img:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-img:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-alter .item-timeline .t-img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  z-index: 7;
  position: relative;
}
.timeline-alter .item-timeline .t-usr-txt {
  display: block;
  padding: 10px;
  position: relative;
  border-color: #e0e6ed;
}
.timeline-alter .item-timeline .t-usr-txt:before {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-usr-txt:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 25px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
}
.timeline-alter .item-timeline .t-usr-txt p {
  margin: 0;
  background: #eceffe;
  height: 45px;
  width: 45px;
  border-radius: 50%;
  display: flex;
  align-self: center;
  justify-content: center;
  margin-bottom: 0;
  color: #4361ee;
  font-weight: 700;
  font-size: 18px;
  z-index: 7;
  position: relative;
}
.timeline-alter .item-timeline .t-usr-txt span {
  align-self: center;
}
.timeline-alter .item-timeline .t-meta-time {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-meta-time p {
  margin: 0;
  min-width: 100px;
  max-width: 100px;
  font-size: 12px;
  font-weight: 700;
  color: #888ea8;
}
.timeline-alter .item-timeline .t-text {
  padding: 10px;
  align-self: center;
}
.timeline-alter .item-timeline .t-text p {
  font-size: 13px;
  margin: 0;
  color: #3b3f5c;
  font-weight: 600;
}
.timeline-alter .item-timeline .t-text p a {
  color: #4361ee;
  font-weight: 600;
}

/*
=======================
    Timeline Simple
=======================
*/
.timeline-simple {
  margin-bottom: 45px;
  max-width: 1140px;
  margin-right: auto;
  margin-left: auto;
}
.timeline-simple h3 {
  font-size: 23px;
  font-weight: 600;
}
.timeline-simple p.timeline-title {
  position: relative;
  font-size: 19px;
  font-weight: 600;
  color: #4361ee;
  margin-bottom: 28px;
}
.timeline-simple .timeline-list p.meta-update-day {
  margin-bottom: 24px;
  font-size: 16px;
  font-weight: 600;
  color: #888ea8;
}
.timeline-simple .timeline-list .timeline-post-content {
  display: flex;
}
.timeline-simple .timeline-list .timeline-post-content > div > div {
  margin-top: 28px;
}
.timeline-simple .timeline-list .timeline-post-content:not(:last-child) > div > div {
  margin-bottom: 70px;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile {
  position: relative;
  z-index: 2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile:after {
  content: "";
  position: absolute;
  border-color: inherit;
  border-width: 2px;
  border-style: solid;
  top: 15px;
  left: 34%;
  transform: translateX(-50%);
  width: 0;
  height: auto;
  top: 48px;
  bottom: -15px;
  border-right-width: 0;
  border-top-width: 0;
  border-bottom-width: 0;
  border-radius: 0;
  z-index: -1;
  border-color: #ebedf2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile img {
  width: 53px;
  height: 53px;
  border-radius: 50%;
  margin-right: 30px;
  -webkit-box-shadow: 0px 4px 9px 0px rgba(31, 45, 61, 0.31);
  box-shadow: 0px 4px 9px 0px rgba(31, 45, 61, 0.31);
}
.timeline-simple .timeline-list .timeline-post-content h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
  color: #4361ee;
}
.timeline-simple .timeline-list .timeline-post-content svg {
  color: #888ea8;
  vertical-align: text-bottom;
  width: 21px;
  height: 21px;
}
.timeline-simple .timeline-list .timeline-post-content:hover svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.09);
}
.timeline-simple .timeline-list .timeline-post-content h6 {
  display: inline-block;
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 11px;
  color: #3b3f5c;
}
.timeline-simple .timeline-list .timeline-post-content:hover h6 {
  color: #888ea8;
}
.timeline-simple .timeline-list .timeline-post-content p.post-text {
  padding-left: 31px;
  color: #888ea8;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 28px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers {
  padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img {
  width: 38px;
  border-radius: 50%;
  margin-right: 7px;
  -webkit-box-shadow: 0px 6px 9px 2px rgba(31, 45, 61, 0.31);
  box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  cursor: pointer;
  margin-bottom: 5px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img:hover {
  -webkit-transform: translateY(-3px) scale(1.02);
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img {
  padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
  width: 20%;
  border-radius: 6px;
  -webkit-box-shadow: 0px 6px 9px 2px rgba(31, 45, 61, 0.31);
  box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
  cursor: pointer;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:hover {
  -webkit-transform: translateY(-3px) scale(1.02);
  transform: translateY(-3px) scale(1.02);
  box-shadow: none;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:not(:last-child) {
  margin-right: 23px;
}

@media (max-width: 767px) {
  .timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
    width: 150px;
    margin-bottom: 23px;
  }
}
@media (max-width: 575px) {
  .timeline-alter .item-timeline {
    display: block;
    text-align: center;
  }
  .timeline-alter .item-timeline .t-meta-time p, .timeline-alter .item-timeline .t-usr-txt p {
    margin: 0 auto;
  }
  .timeline-simple .timeline-list .timeline-post-content {
    display: block;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile {
    margin-bottom: 18px;
    text-align: center;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile:after {
    display: none;
  }
  .timeline-simple .timeline-list .timeline-post-content div.user-profile img {
    margin-right: 0;
  }
  .timeline-simple .timeline-list .timeline-post-content h4, .timeline-simple .timeline-list .timeline-post-content .meta-time-date {
    text-align: center;
  }
}
/*
=======================
    Timeline Simple
=======================
*/
.timeline {
  width: 85%;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  padding: 32px 0 32px 32px;
  border-left: 2px solid #eceffe;
  font-size: 15px;
}

.timeline-item {
  display: flex;
  gap: 24px;
}
.timeline-item + * {
  margin-top: 24px;
}
.timeline-item + .extra-space {
  margin-top: 48px;
}

.new-comment {
  width: 100%;
}

.timeline-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: -52px;
  flex-shrink: 0;
  overflow: hidden;
  box-shadow: 0 0 0 6px #e0e6ed;
}
.timeline-item-icon svg {
  width: 20px;
  height: 20px;
}
.timeline-item-icon.faded-icon {
  background-color: white;
  color: #0e1726;
}
.timeline-item-icon.filled-icon {
  background-color: #4361ee;
  color: #fff;
}

.timeline-item-description {
  display: flex;
  gap: 8px;
  color: #3b3f5c;
}
.timeline-item-description img {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  aspect-ratio: 1/1;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}
.timeline-item-description a {
  color: #4361ee;
  font-weight: 600;
  text-decoration: none;
}
.timeline-item-description a:hover, .timeline-item-description a:focus {
  outline: 0;
  color: #888ea8;
}

.comment {
  margin-top: 12px;
  color: #3b3f5c;
  border-radius: 6px;
  padding: 16px;
  font-size: 1rem;
  border: 1px solid #e0e6ed;
  box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
}
.comment .btn-like {
  padding: 7px 13px;
  border: none;
  box-shadow: none;
  border-radius: 60px;
}
.comment .btn-like svg {
  width: 19px;
  height: 19px;
  vertical-align: sub;
}
.comment p {
  color: #515365;
}

.btn.square {
  background: transparent;
}
.btn.square svg {
  width: 24px;
  height: 24px;
  fill: #e2a03f;
  color: #0e1726;
}

.show-replies {
  color: #888ea8;
  background-color: transparent;
  border: 0;
  padding: 0;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1rem;
  cursor: pointer;
}
.show-replies svg {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}
.show-replies:hover, .show-replies:focus {
  color: #3b3f5c;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
