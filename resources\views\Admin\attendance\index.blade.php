@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header flex justify-between items-center">
            <h3 class="text-lg font-semibold">Attendance Records</h3>
            <form class="flex gap-2" method="GET" action="{{ route('attendance.index') }}">
                <div class="space-between">
                    <input type="date" name="date" value="{{ $filters['date'] ?? '' }}" class="form-input">

                    <select name="staff_id" class="form-select mt-2">
                        <option value="">All Staff</option>
                        @foreach ($staff as $s)
                            <option value="{{ $s->id }}" @selected(($filters['staff_id'] ?? null) == $s->id)>
                                {{ $s->name }}
                            </option>
                        @endforeach
                    </select>

                    <select name="company_id" class="form-select mt-2">
                        <option value="">All Companies</option>
                        @foreach ($companies as $c)
                            <option value="{{ $c->id }}" @selected(($filters['company_id'] ?? null) == $c->id)>
                                {{ $c->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <button type="submit" class="btn btn-primary" style="margin-top: 9px">Filter</button>
            </form>
        </div>

        <div class="card-body overflow-auto">
            <table class="table table-bordered table-striped text-sm">
                <thead>
                    <tr>
                        <th>Staff</th>
                        <th>Company</th>
                        <th>Login Time</th>
                        <th>Logout Time</th>
                        <th>Duration</th>
                        <th>Rate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($records as $r)
                        <tr>
                            <td>{{ $r->user->name }}</td>
                            <td>{{ $r->company->name }}</td>
                            <td>{{ \Carbon\Carbon::parse($r->login_time)->format('d M Y, H:i') }}</td>
                            <td>{{ $r->logout_time ? \Carbon\Carbon::parse($r->logout_time)->format('d M Y, H:i') : '-' }}
                            </td>
                            <td>
                                @if ($r->logout_time)
                                    {{ \Carbon\Carbon::parse($r->login_time)->diffInMinutes($r->logout_time) }} mins
                                @else
                                    In Progress
                                @endif
                            </td>
                            <td>{{ ucfirst($r->rate_type) }}</td>
                            <td>
                                @can('edit-attendance', $r)
                                    <a href="{{ route('attendance.edit', $r) }}" class="btn btn-sm btn-secondary">Edit</a>
                                @endcan
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center text-gray-500">No records found</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            <div class="mt-4">{{ $records->withQueryString()->links() }}</div>
        </div>
    </div>
@endsection
