<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\Shift;
use App\Notifications\ShiftReminderNotification;
use Illuminate\Support\Facades\Log;

class SendShiftReminders implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Get shifts that need reminders (starting within 2 hours and reminder not sent)
        $shifts = Shift::needingReminder()->with(['user', 'company'])->get();

        $remindersSent = 0;

        foreach ($shifts as $shift) {
            try {
                // Send notification to the user
                $shift->user->notify(new ShiftReminderNotification($shift));

                // Mark reminder as sent
                $shift->update(['reminder_sent' => true]);

                $remindersSent++;

                Log::info('Shift reminder sent', [
                    'shift_id' => $shift->id,
                    'user_id' => $shift->user_id,
                    'user_name' => $shift->user->name,
                    'company' => $shift->company->name,
                    'start_time' => $shift->start_time,
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to send shift reminder', [
                    'shift_id' => $shift->id,
                    'user_id' => $shift->user_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Shift reminders job completed', [
            'total_reminders_sent' => $remindersSent,
            'total_shifts_checked' => $shifts->count(),
        ]);
    }
}
