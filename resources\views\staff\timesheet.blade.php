@extends('layouts.app')

@section('content')
    <div class="row layout-top-spacing">
        <div class="col-12">
            <div class="widget-content widget-content-area br-8">

                {{-- Heading + View Payroll Button --}}
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-0">
                            {{-- Timesheet for {{ $user->name ?? 'N/A' }} --}}
                            @role('Admin')
                                <small class="text-muted">Staf Data</small>
                            @endrole
                        </h4>
                    </div>

                    <div class="d-flex align-items-center gap-2">
                        {{-- View Payroll Button --}}
                        <a href="{{ route('payroll.show', $user->id) }}" class="btn btn-primary">
                            View Payroll
                        </a>

                        @role('Admin')
                            {{-- Staff selector for Admin --}}
                            <form method="GET" action="{{ route('staff.timesheet') }}">
                                <div class="input-group">
                                    <select name="user_id" class="form-select" onchange="this.form.submit()">
                                        <option value="">-- Select Staff --</option>
                                        @foreach (\App\Models\User::role('Staff')->get() as $staff)
                                            <option value="{{ $staff->id }}"
                                                {{ request('user_id') == $staff->id ? 'selected' : '' }}>
                                                {{ $staff->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </form>
                        @endrole
                    </div>
                </div>

                {{-- Timesheet Table --}}
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Company</th>
                            <th>Login Time</th>
                            <th>Logout Time</th>
                            <th>Total Hours</th>
                            <th>Status</th>
                            <th>Rate Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($logins as $login)
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($login->login_time)->format('Y-m-d') }}</td>
                                <td>{{ $login->company->name ?? 'N/A' }}</td>
                                <td>{{ \Carbon\Carbon::parse($login->login_time)->format('h:i A') }}</td>
                                <td>{{ $login->logout_time ? \Carbon\Carbon::parse($login->logout_time)->format('h:i A') : 'Active' }}
                                </td>
                                <td>{{ $login->work_hours ? round($login->work_hours, 2) . 'h' : '-' }}</td>
                                <td>
                                    <span class="badge bg-{{ match ($login->status) {
                                        'on_time' => 'success',
                                        'late_in' => 'warning',
                                        'left_early' => 'info',
                                        'absent' => 'danger',
                                        default => 'success',
                                    } }}">{{ ucfirst($login->status ?? 'Present') }}</span>
                                </td>
                                <td>{{ ucfirst($login->rate_type) }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showDetails({{ $login->id }})">
                                        <i class="fas fa-eye"></i> Details
                                    </button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center">No timesheet records found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Details Modal -->
        <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="detailsModalLabel">Shift Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="detailsContent">
                        <!-- Content will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Details modal functionality
        async function showDetails(loginId) {
            try {
                const response = await fetch(`/staff/timesheet/${loginId}/details`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    const content = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Basic Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Date:</strong></td><td>${data.date}</td></tr>
                                    <tr><td><strong>Company:</strong></td><td>${data.company}</td></tr>
                                    <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(data.status)}">${data.status}</span></td></tr>
                                    <tr><td><strong>Rate Type:</strong></td><td>${data.rate_type}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Time Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Clock In:</strong></td><td>${data.login_time}</td></tr>
                                    <tr><td><strong>Clock Out:</strong></td><td>${data.logout_time}</td></tr>
                                    <tr><td><strong>Total Work:</strong></td><td>${data.total_work_hours}</td></tr>
                                    <tr><td><strong>Total Breaks:</strong></td><td>${data.total_pause_minutes}</td></tr>
                                </table>
                            </div>
                        </div>
                        ${data.pause_periods && data.pause_periods.length > 0 ? `
                            <div class="mt-3">
                                <h6 class="text-primary">Break History</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Reason</th>
                                                <th>Start Time</th>
                                                <th>End Time</th>
                                                <th>Duration</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.pause_periods.map(period => `
                                                <tr>
                                                    <td>${period.reason}</td>
                                                    <td>${new Date(period.start).toLocaleTimeString()}</td>
                                                    <td>${new Date(period.end).toLocaleTimeString()}</td>
                                                    <td>${period.duration_minutes} min</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        ` : ''}
                        ${data.is_active ? '<div class="alert alert-info mt-3"><i class="fas fa-clock"></i> This shift is currently active.</div>' : ''}
                    `;

                    document.getElementById('detailsContent').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                } else {
                    alert('Failed to load details');
                }
            } catch (error) {
                console.error('Error loading details:', error);
                alert('Failed to load details');
            }
        }

        function getStatusColor(status) {
            switch(status.toLowerCase()) {
                case 'present':
                case 'on_time': return 'success';
                case 'late_in': return 'warning';
                case 'left_early': return 'info';
                case 'absent': return 'danger';
                default: return 'secondary';
            }
        }
    </script>
@endpush
