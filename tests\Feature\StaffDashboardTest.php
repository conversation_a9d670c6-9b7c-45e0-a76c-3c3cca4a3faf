<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Company;
use App\Models\Shift;
use App\Models\StaffLogin;
use App\Models\Application;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class StaffDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'Admin']);
        Role::create(['name' => 'Staff']);
        Role::create(['name' => 'Team Leader']);
    }

    /** @test */
    public function staff_can_access_dashboard()
    {
        $company = Company::factory()->create();
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard.staff');
    }

    /** @test */
    public function staff_dashboard_displays_statistics()
    {
        $company = Company::factory()->create();
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        // Create test data
        Shift::create([
            'user_id' => $staff->id,
            'company_id' => $company->id,
            'start_time' => now()->addHour(),
            'end_time' => now()->addHours(9),
            'status' => 'scheduled'
        ]);

        Application::create([
            'user_id' => $staff->id,
            'title' => 'Personal Leave Request',
            'application_date' => now()->addDay(),
            'description' => 'Personal leave for family matters',
            'status' => 'pending'
        ]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Staff Dashboard');
        $response->assertSee('Today\'s Shifts');
        $response->assertSee('Upcoming Shifts');
        $response->assertSee('Total Applications');
    }

    /** @test */
    public function staff_dashboard_shows_current_shift_info()
    {
        $company = Company::factory()->create(['name' => 'Test Company']);
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        // Create a current shift
        Shift::create([
            'user_id' => $staff->id,
            'company_id' => $company->id,
            'start_time' => now()->subHour(),
            'end_time' => now()->addHours(7),
            'status' => 'scheduled'
        ]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Test Company');
        $response->assertSee('Current Shift Information');
    }

    /** @test */
    public function staff_dashboard_shows_no_active_shift_message()
    {
        $company = Company::factory()->create();
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('No active shift at the moment');
        $response->assertSee('View Schedule');
    }

    /** @test */
    public function staff_dashboard_displays_recent_shifts()
    {
        $company = Company::factory()->create(['name' => 'Recent Company']);
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        // Create recent shifts
        Shift::create([
            'user_id' => $staff->id,
            'company_id' => $company->id,
            'start_time' => now()->subDays(2),
            'end_time' => now()->subDays(2)->addHours(8),
            'status' => 'completed'
        ]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Recent Shifts');
        $response->assertSee('Recent Company');
    }

    /** @test */
    public function non_staff_users_cannot_see_staff_dashboard()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard.admin');
        $response->assertDontSee('Staff Dashboard');
    }

    /** @test */
    public function staff_dashboard_includes_play_button_functionality()
    {
        $company = Company::factory()->create();
        $staff = User::factory()->create();
        $staff->assignRole('Staff');
        $staff->companies()->attach($company->id, ['hourly_rate' => 15.00]);

        $response = $this->actingAs($staff)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('play-button');
        $response->assertSee('togglePlayPause');
        $response->assertSee('Ready to Start Shift');
    }
}
