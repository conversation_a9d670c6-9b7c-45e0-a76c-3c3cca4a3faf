@extends('layouts.app')

@push('styles')
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link rel="stylesheet" type="text/css" href="src/assets/css/light/elements/alert.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/dark/elements/alert.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/light/dashboard/dash_1.css">
    <link rel="stylesheet" type="text/css" href="src/assets/css/dark/dashboard/dash_1.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <style>
        /* Global Dashboard Styling */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container-fluid {
            position: relative;
            z-index: 1;
        }

        /* Add subtle texture overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
            color: white;
            padding: 3rem 2.5rem;
            border-radius: 20px;
            margin-bottom: 2.5rem;
            box-shadow: 0 15px 35px rgba(233, 168, 82, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            background-size: 200% 200%;
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 200% 200%; }
            50% { background-position: -200% -200%; }
        }

        .dashboard-header h1 {
            margin: 0;
            font-size: 3.2rem;
            font-weight: 800;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
        }

        .dashboard-header p {
            margin: 1rem 0 0 0;
            opacity: 0.95;
            font-size: 1.3rem;
            font-weight: 300;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(233, 168, 82, 0.1), transparent);
            transition: left 0.6s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card .stat-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
            box-shadow: 0 8px 25px rgba(233, 168, 82, 0.3);
        }

        .stat-card .stat-icon i {
            font-size: 2rem;
            color: white;
        }

        .stat-card .stat-number {
            font-size: 2.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            position: relative;
            z-index: 1;
        }

        .stat-card .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Shift Management Container */
        .shift-management-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 3rem 2.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            overflow: hidden;
        }

        .shift-management-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #E9A852, #D4941F, #E9A852);
            background-size: 200% 100%;
            animation: shimmer-bar 3s ease-in-out infinite;
        }

        @keyframes shimmer-bar {
            0%, 100% { background-position: 200% 0; }
            50% { background-position: -200% 0; }
        }

        .shift-control-btn {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
            color: white;
            font-size: 3rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 15px 35px rgba(233, 168, 82, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .shift-control-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .shift-control-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .shift-control-btn:hover {
            transform: scale(1.08) rotate(5deg);
            box-shadow: 0 25px 50px rgba(233, 168, 82, 0.6), 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .shift-control-btn:active {
            transform: scale(0.95);
        }

        .shift-control-btn.stop-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .shift-control-btn.stop-btn:hover {
            box-shadow: 0 25px 50px rgba(231, 76, 60, 0.6), 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .shift-control-btn.pause-btn {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 15px 35px rgba(243, 156, 18, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .shift-control-btn.pause-btn:hover {
            box-shadow: 0 25px 50px rgba(243, 156, 18, 0.6), 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .shift-status {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .shift-status.active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .shift-status.inactive {
            color: #7f8c8d;
        }

        .shift-status.paused {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .timer-display {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 3.5rem;
            font-weight: 800;
            text-align: center;
            margin-top: 1.5rem;
            letter-spacing: 3px;
            box-shadow: inset 0 8px 16px rgba(0, 0, 0, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .timer-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: timer-glow 2s ease-in-out infinite;
        }

        @keyframes timer-glow {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .activity-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .activity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #E9A852, #D4941F, #E9A852);
            background-size: 200% 100%;
            animation: shimmer-bar 3s ease-in-out infinite;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .activity-card h5 {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            font-weight: 700;
            font-size: 1.4rem;
            position: relative;
            z-index: 1;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(236, 240, 241, 0.5);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .activity-item:hover {
            background: rgba(233, 168, 82, 0.05);
            border-radius: 12px;
            padding: 1rem;
            margin: 0 -1rem;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #E9A852, #D4941F);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1.5rem;
            box-shadow: 0 8px 20px rgba(233, 168, 82, 0.3);
        }

        .activity-icon i {
            color: white;
            font-size: 1.2rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .activity-time {
            color: #95a5a6;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Modern Button Styles */
        .btn-modern {
            background: linear-gradient(135deg, #E9A852, #D4941F);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 2rem;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(233, 168, 82, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .btn-modern:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(233, 168, 82, 0.4);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard-header h1 {
                font-size: 2.5rem;
            }

            .dashboard-header p {
                font-size: 1.1rem;
            }

            .shift-control-btn {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }

            .timer-display {
                font-size: 2.5rem;
                padding: 2rem;
            }

            .stat-card {
                padding: 2rem 1.5rem;
            }

            .activity-card {
                padding: 2rem;
            }
        }

        /* Enhanced Control Center */
        .shift-control-center {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        /* Pulse animation for paused state */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .shift-control-btn.paused {
            animation: pulse 2s infinite;
        }

        /* Pause Button Styling */
        .pause-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            font-size: 1.8rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 25px rgba(243, 156, 18, 0.4), 0 3px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .pause-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .pause-btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .pause-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(243, 156, 18, 0.6), 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        /* Status Display Styling */
        .shift-status-display {
            margin-bottom: 2rem;
        }

        .shift-status-text {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .shift-status-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 1.5rem;
        }

        .timer-label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
        }

        /* Pause Info Styling */
        .pause-info {
            background: rgba(243, 156, 18, 0.1);
            border: 2px solid rgba(243, 156, 18, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .pause-reason {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e67e22;
            margin-bottom: 0.5rem;
        }

        .pause-duration {
            font-size: 1rem;
            color: #7f8c8d;
            font-weight: 600;
        }

        /* Quick Actions Styling */
        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        /* Enhanced Quick Action Buttons */
        .quick-action-btn {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #E9A852;
            color: #E9A852;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
            box-shadow: 0 8px 20px rgba(233, 168, 82, 0.2);
            position: relative;
            overflow: hidden;
        }

        .quick-action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(233, 168, 82, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .quick-action-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .quick-action-btn:hover {
            background: #E9A852;
            color: white;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(233, 168, 82, 0.4);
            border-color: #D4941F;
        }

        .quick-action-btn i {
            font-size: 1.1rem;
        }
    </style>
@endpush
@section('content')
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="text-white"><i class="fas fa-user-clock me-3 text-white"></i>Staff Dashboard</h1>
                    <p class="text-white">Welcome back, {{ Auth::user()->name }}! Manage your shifts and track your work hours.</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white">
                        <div style="font-size: 1.2rem; font-weight: 600;">{{ date('l, F j, Y') }}</div>
                        <div style="font-size: 1rem; opacity: 0.9;" id="current-time">{{ date('h:i:s A') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Shift Management Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="shift-management-container">
                    <!-- Main Control Button -->
                    <div class="shift-control-center">
                        <button class="shift-control-btn" id="mainShiftBtn" onclick="handleMainShiftAction()">
                            <i class="fas fa-play" id="mainShiftIcon"></i>
                        </button>

                        <!-- Pause Button (hidden by default) -->
                        <button class="pause-btn d-none" id="pauseBtn" onclick="handlePauseAction()">
                            <i class="fas fa-pause"></i>
                        </button>
                    </div>

                    <!-- Status Display -->
                    <div class="shift-status-display">
                        <div class="shift-status-text" id="shiftStatusText">Ready to Start Shift</div>
                        <div class="shift-status-subtitle" id="shiftStatusSubtitle">Click the play button to begin your shift</div>

                        <!-- Timer Display -->
                        <div class="timer-display d-none" id="timerDisplay">
                            <div class="timer-main">
                                <span id="timer-hours">00</span>:<span id="timer-minutes">00</span>:<span id="timer-seconds">00</span>
                            </div>
                            <div class="timer-label">Active Work Time</div>
                        </div>

                        <!-- Pause Info -->
                        <div class="pause-info d-none" id="pauseInfo">
                            <div class="pause-reason" id="pauseReason"></div>
                            <div class="pause-duration" id="pauseDuration">Paused for: <span id="pauseTimer">00:00</span></div>
                        </div>
                    </div>

                    <!-- Quick Actions (shown when shift is active) -->
                    <div class="quick-actions d-none" id="quickActions">
                        <button class="quick-action-btn" onclick="showPauseDialog()">
                            <i class="fas fa-coffee"></i>
                            <span>Take Break</span>
                        </button>
                        <button class="quick-action-btn" onclick="showEndShiftDialog()">
                            <i class="fas fa-stop"></i>
                            <span>End Shift</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-number" id="todayShiftsCount">{{ $todayShifts }}</div>
                    <div class="stat-label">Today's Shifts</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number" id="todayWorkHours">{{ number_format($todayWorkHours / 60, 1) }}h</div>
                    <div class="stat-label">Today's Work Hours</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="stat-number">{{ $upcomingShifts }}</div>
                    <div class="stat-label">Upcoming Shifts</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-number">{{ $totalApplications }}</div>
                    <div class="stat-label">Total Applications</div>
                </div>
            </div>
        </div>

        <!-- Current Shift Info and Recent Activity -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="activity-card">
                    <h5><i class="fas fa-info-circle me-2"></i>Current Shift Information</h5>
                    @if($currentShift)
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ $currentShift->company->name }}</div>
                                <div class="activity-time">Current shift location</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ $currentShift->start_time->format('h:i A') }} - {{ $currentShift->end_time->format('h:i A') }}</div>
                                <div class="activity-time">Shift duration</div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times text-muted" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <p class="text-muted">No active shift at the moment</p>
                            <a href="{{ route('staff.timetable') }}" class="btn btn-outline-primary">View Schedule</a>
                        </div>
                    @endif
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="activity-card">
                    <h5><i class="fas fa-history me-2"></i>Recent Shifts</h5>
                    @if($recentShifts->count() > 0)
                        @foreach($recentShifts->take(4) as $shift)
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">{{ $shift->company->name ?? 'Unknown Company' }}</div>
                                    <div class="activity-time">{{ $shift->start_time->format('M j, Y - h:i A') }}</div>
                                </div>
                            </div>
                        @endforeach
                        <div class="text-center mt-3">
                            <a href="{{ route('staff.timesheet') }}" class="btn btn-outline-primary btn-sm">View All Shifts</a>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-alt text-muted" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <p class="text-muted">No recent shifts found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <!-- SweetAlert2 for confirmation dialogs -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Global shift management state
        let shiftState = {
            hasActiveShift: false,
            status: 'inactive', // inactive, active, paused
            shiftId: null,
            startTime: null,
            currentSessionMinutes: 0,
            totalPauseMinutes: 0,
            currentPauseStart: null,
            currentPauseReason: null
        };

        let timerInterval = null;
        let pauseTimerInterval = null;
        let statsUpdateInterval = null;

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeShiftManagement();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // Update stats every 30 seconds
            statsUpdateInterval = setInterval(updateTodayStats, 30000);
        });

        // Initialize shift management system
        async function initializeShiftManagement() {
            try {
                const response = await fetch('/shift/status');
                const data = await response.json();

                if (data.success) {
                    // Map API response to JavaScript state (handle snake_case to camelCase)
                    shiftState.hasActiveShift = data.data.has_active_shift;
                    shiftState.status = data.data.status;
                    shiftState.shiftId = data.data.shift_id;
                    shiftState.currentSessionMinutes = data.data.current_session_minutes || 0;
                    shiftState.totalPauseMinutes = data.data.total_pause_minutes || 0;
                    shiftState.currentPauseReason = data.data.current_pause_reason;

                    // Parse start_time and current_pause_start as Date objects
                    if (data.data.start_time) {
                        shiftState.startTime = new Date(data.data.start_time);
                    }
                    if (data.data.current_pause_start) {
                        shiftState.currentPauseStart = new Date(data.data.current_pause_start);
                    }

                    updateShiftUI();
                    updateTodayStats();

                    if (shiftState.hasActiveShift) {
                        if (shiftState.status === 'active') {
                            startTimers();
                        } else if (shiftState.status === 'paused') {
                            startPauseTimer();
                        }
                    }
                }
            } catch (error) {
                console.error('Failed to initialize shift management:', error);
            }
        }

        // Main shift action handler
        function handleMainShiftAction() {
            if (!shiftState.hasActiveShift) {
                showStartShiftDialog();
            } else if (shiftState.status === 'paused') {
                resumeShift();
            } else {
                showEndShiftDialog();
            }
        }

        // Pause action handler
        function handlePauseAction() {
            if (shiftState.status === 'active') {
                showPauseDialog();
            }
        }

        // Show start shift confirmation
        function showStartShiftDialog() {
            Swal.fire({
                title: 'Start Your Shift',
                text: 'Are you sure you want to start your shift?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#E9A852',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Start Shift',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    startShift();
                }
            });
        }

        // Show pause dialog with reason selection
        function showPauseDialog() {
            Swal.fire({
                title: 'Pause Your Shift',
                text: 'Please select a reason for pausing your shift:',
                input: 'select',
                inputOptions: {
                    'Lunch Break': 'Lunch Break',
                    'Toilet Break': 'Toilet Break',
                    'Personal Break': 'Personal Break',
                    'Other': 'Other'
                },
                inputPlaceholder: 'Select a reason',
                showCancelButton: true,
                confirmButtonColor: '#E9A852',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Pause Shift',
                cancelButtonText: 'Cancel',
                inputValidator: (value) => {
                    if (!value) {
                        return 'Please select a reason for the pause';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    pauseShift(result.value);
                }
            });
        }

        // Show end shift confirmation
        function showEndShiftDialog() {
            Swal.fire({
                title: 'End Your Shift',
                text: 'Are you sure you want to end your current shift? This action cannot be undone.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, End Shift',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    endShift();
                }
            });
        }

        // API Functions
        async function startShift() {
            try {
                const response = await fetch('/shift/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    shiftState.hasActiveShift = true;
                    shiftState.status = 'active';
                    shiftState.shiftId = data.data.shift_id;
                    shiftState.startTime = new Date(data.data.start_time);
                    shiftState.currentSessionMinutes = 0;

                    updateShiftUI();
                    startTimers();
                    updateTodayStats();

                    Swal.fire({
                        title: 'Shift Started!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    // If there's already an active shift, refresh the current state
                    if (data.message.includes('already have an active shift')) {
                        await initializeShiftManagement();
                        Swal.fire({
                            title: 'Active Shift Found',
                            text: 'You already have an active shift running. The interface has been updated.',
                            icon: 'info',
                            timer: 3000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire('Error', data.message, 'error');
                    }
                }
            } catch (error) {
                console.error('Error starting shift:', error);
                Swal.fire('Error', 'Failed to start shift. Please try again.', 'error');
            }
        }

        async function pauseShift(reason) {
            try {
                const response = await fetch('/shift/pause', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ reason: reason })
                });

                const data = await response.json();

                if (data.success) {
                    shiftState.status = 'paused';
                    shiftState.currentPauseStart = new Date(data.data.pause_start);
                    shiftState.currentPauseReason = reason;

                    updateShiftUI();
                    startPauseTimer();

                    Swal.fire({
                        title: 'Shift Paused!',
                        text: data.message,
                        icon: 'info',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire('Error', data.message, 'error');
                }
            } catch (error) {
                console.error('Error pausing shift:', error);
                Swal.fire('Error', 'Failed to pause shift. Please try again.', 'error');
            }
        }

        async function resumeShift() {
            try {
                const response = await fetch('/shift/resume', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    shiftState.status = 'active';
                    shiftState.totalPauseMinutes = data.data.total_pause_minutes;
                    shiftState.currentPauseStart = null;
                    shiftState.currentPauseReason = null;

                    updateShiftUI();
                    stopPauseTimer();

                    Swal.fire({
                        title: 'Shift Resumed!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire('Error', data.message, 'error');
                }
            } catch (error) {
                console.error('Error resuming shift:', error);
                Swal.fire('Error', 'Failed to resume shift. Please try again.', 'error');
            }
        }

        async function endShift() {
            try {
                const response = await fetch('/shift/end', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    shiftState.hasActiveShift = false;
                    shiftState.status = 'inactive';
                    shiftState.shiftId = null;
                    shiftState.startTime = null;

                    updateShiftUI();
                    stopAllTimers();
                    updateTodayStats();

                    Swal.fire({
                        title: 'Shift Ended!',
                        html: `${data.message}<br><br><strong>Total Work Hours:</strong> ${data.data.total_work_hours}h`,
                        icon: 'success',
                        timer: 4000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire('Error', data.message, 'error');
                }
            } catch (error) {
                console.error('Error ending shift:', error);
                Swal.fire('Error', 'Failed to end shift. Please try again.', 'error');
            }
        }

        // Timer Management Functions
        function startTimers() {
            if (timerInterval) clearInterval(timerInterval);
            timerInterval = setInterval(updateWorkTimer, 1000);
            updateWorkTimer();
        }

        function startPauseTimer() {
            if (pauseTimerInterval) clearInterval(pauseTimerInterval);
            pauseTimerInterval = setInterval(updatePauseTimer, 1000);
            updatePauseTimer();
        }

        function stopPauseTimer() {
            if (pauseTimerInterval) {
                clearInterval(pauseTimerInterval);
                pauseTimerInterval = null;
            }
        }

        function stopAllTimers() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
            stopPauseTimer();
        }

        function updateWorkTimer() {
            if (!shiftState.startTime || shiftState.status !== 'active') return;

            const now = new Date();
            const totalMinutes = Math.floor((now - shiftState.startTime) / (1000 * 60));
            const workMinutes = Math.max(0, totalMinutes - shiftState.totalPauseMinutes);

            const hours = Math.floor(workMinutes / 60);
            const minutes = workMinutes % 60;
            const seconds = Math.floor((now - shiftState.startTime) / 1000) % 60;

            document.getElementById('timer-hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('timer-minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('timer-seconds').textContent = seconds.toString().padStart(2, '0');
        }

        function updatePauseTimer() {
            if (!shiftState.currentPauseStart) return;

            const now = new Date();
            const pauseMinutes = Math.floor((now - shiftState.currentPauseStart) / (1000 * 60));
            const pauseSeconds = Math.floor((now - shiftState.currentPauseStart) / 1000) % 60;

            const pauseTimerElement = document.getElementById('pauseTimer');
            if (pauseTimerElement) {
                pauseTimerElement.textContent = `${pauseMinutes.toString().padStart(2, '0')}:${pauseSeconds.toString().padStart(2, '0')}`;
            }
        }

        // UI Update Functions
        function updateShiftUI() {
            const mainBtn = document.getElementById('mainShiftBtn');
            const mainIcon = document.getElementById('mainShiftIcon');
            const pauseBtn = document.getElementById('pauseBtn');
            const statusText = document.getElementById('shiftStatusText');
            const statusSubtitle = document.getElementById('shiftStatusSubtitle');
            const timerDisplay = document.getElementById('timerDisplay');
            const pauseInfo = document.getElementById('pauseInfo');
            const quickActions = document.getElementById('quickActions');

            if (!shiftState.hasActiveShift) {
                // No active shift
                mainBtn.className = 'shift-control-btn';
                mainIcon.className = 'fas fa-play';
                pauseBtn.classList.add('d-none');
                statusText.textContent = 'Ready to Start Shift';
                statusSubtitle.textContent = 'Click the play button to begin your shift';
                timerDisplay.classList.add('d-none');
                pauseInfo.classList.add('d-none');
                quickActions.classList.add('d-none');
            } else if (shiftState.status === 'active') {
                // Active shift
                mainBtn.className = 'shift-control-btn active';
                mainIcon.className = 'fas fa-stop';
                pauseBtn.classList.remove('d-none');
                statusText.textContent = 'Shift Active';
                statusSubtitle.textContent = 'Your shift is currently running';
                timerDisplay.classList.remove('d-none');
                pauseInfo.classList.add('d-none');
                quickActions.classList.remove('d-none');
            } else if (shiftState.status === 'paused') {
                // Paused shift
                mainBtn.className = 'shift-control-btn paused';
                mainIcon.className = 'fas fa-play';
                pauseBtn.classList.add('d-none');
                statusText.textContent = 'Shift Paused';
                statusSubtitle.textContent = 'Click to resume your shift';
                timerDisplay.classList.remove('d-none');
                pauseInfo.classList.remove('d-none');
                quickActions.classList.remove('d-none');

                // Update pause info
                document.getElementById('pauseReason').textContent = `Reason: ${shiftState.currentPauseReason}`;
            }
        }

        // Statistics Update Functions
        async function updateTodayStats() {
            try {
                const response = await fetch('/shift/today-stats');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('todayShiftsCount').textContent = data.data.today_shifts;
                    document.getElementById('todayWorkHours').textContent = `${data.data.today_work_hours}h`;
                }
            } catch (error) {
                console.error('Failed to update today stats:', error);
            }
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // Add smooth animations to stat cards
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
@endpush
