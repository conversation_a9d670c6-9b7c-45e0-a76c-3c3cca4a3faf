<?php

namespace App\Http\Controllers\Staff;

use App\Http\Controllers\Controller;
use App\Models\StaffLogin;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShiftManagementController extends Controller
{
    /**
     * Start a new shift (supports multi-company)
     */
    public function startShift(Request $request)
    {
        try {
            $user = Auth::user();
            $companyIds = $request->input('company_ids', []);

            // If no companies specified, use primary company
            if (empty($companyIds)) {
                $company = $user->companies()->first();
                if (!$company) {
                    return response()->json([
                        'success' => false,
                        'message' => 'No company assigned. Please contact your administrator.'
                    ], 400);
                }
                $companyIds = [$company->id];
            }

            // Validate that user has access to all requested companies
            $userCompanyIds = $user->companies()->pluck('companies.id')->toArray();
            $invalidCompanies = array_diff($companyIds, $userCompanyIds);

            if (!empty($invalidCompanies)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have access to one or more selected companies.'
                ], 400);
            }

            // Check for existing active shifts for these companies
            $existingShifts = StaffLogin::where('user_id', $user->id)
                ->whereIn('company_id', $companyIds)
                ->whereNull('logout_time')
                ->where('shift_status', '!=', 'ended')
                ->get();

            if ($existingShifts->isNotEmpty()) {
                $companyNames = $existingShifts->pluck('company.name')->join(', ');
                return response()->json([
                    'success' => false,
                    'message' => "You already have active shifts for: {$companyNames}"
                ], 400);
            }

            $createdShifts = [];
            $now = now();

            // Create shifts for each company
            foreach ($companyIds as $companyId) {
                $company = $user->companies()->where('companies.id', $companyId)->first();

                // Determine rate type based on multi-company login
                $rateType = count($companyIds) > 1 ? 'multi_company' : 'regular';

                $staffLogin = StaffLogin::create([
                    'user_id' => $user->id,
                    'company_id' => $companyId,
                    'login_time' => $now,
                    'rate_type' => $rateType,
                    'shift_status' => 'active',
                    'status' => 'on_time',
                    'pause_periods' => [],
                    'total_pause_minutes' => 0
                ]);

                $createdShifts[] = [
                    'shift_id' => $staffLogin->id,
                    'company_name' => $company->name,
                    'rate_type' => $rateType
                ];
            }

            $message = count($companyIds) > 1
                ? 'Multi-company shifts started successfully!'
                : 'Shift started successfully!';

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'shifts' => $createdShifts,
                    'start_time' => $now->toISOString(),
                    'is_multi_company' => count($companyIds) > 1
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start shift. Please try again.'
            ], 500);
        }
    }

    /**
     * Pause current shift
     */
    public function pauseShift(Request $request)
    {
        try {
            $request->validate([
                'reason' => 'required|string|max:255'
            ]);

            $user = Auth::user();
            
            $activeShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->where('shift_status', 'active')
                ->first();

            if (!$activeShift) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active shift found to pause.'
                ], 400);
            }

            // Update shift to paused status
            $activeShift->update([
                'shift_status' => 'paused',
                'current_pause_reason' => $request->reason,
                'current_pause_start' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Shift paused successfully!',
                'data' => [
                    'pause_start' => now()->toISOString(),
                    'reason' => $request->reason
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to pause shift. Please try again.'
            ], 500);
        }
    }

    /**
     * Resume paused shift
     */
    public function resumeShift(Request $request)
    {
        try {
            $user = Auth::user();
            
            $pausedShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->where('shift_status', 'paused')
                ->first();

            if (!$pausedShift) {
                return response()->json([
                    'success' => false,
                    'message' => 'No paused shift found to resume.'
                ], 400);
            }

            // Calculate pause duration
            $pauseStart = $pausedShift->current_pause_start;
            $pauseEnd = now();
            $pauseDurationMinutes = $pauseStart->diffInMinutes($pauseEnd);

            // Update pause periods
            $pausePeriods = $pausedShift->pause_periods ?? [];
            $pausePeriods[] = [
                'reason' => $pausedShift->current_pause_reason,
                'start' => $pauseStart->toISOString(),
                'end' => $pauseEnd->toISOString(),
                'duration_minutes' => $pauseDurationMinutes
            ];

            // Update shift to active status
            $pausedShift->update([
                'shift_status' => 'active',
                'pause_periods' => $pausePeriods,
                'total_pause_minutes' => $pausedShift->total_pause_minutes + $pauseDurationMinutes,
                'current_pause_reason' => null,
                'current_pause_start' => null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Shift resumed successfully!',
                'data' => [
                    'pause_duration_minutes' => $pauseDurationMinutes,
                    'total_pause_minutes' => $pausedShift->total_pause_minutes + $pauseDurationMinutes
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to resume shift. Please try again.'
            ], 500);
        }
    }

    /**
     * End current shift
     */
    public function endShift(Request $request)
    {
        try {
            $user = Auth::user();
            
            $activeShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->whereIn('shift_status', ['active', 'paused'])
                ->first();

            if (!$activeShift) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active shift found to end.'
                ], 400);
            }

            // If shift is currently paused, calculate final pause duration
            if ($activeShift->shift_status === 'paused' && $activeShift->current_pause_start) {
                $pauseStart = $activeShift->current_pause_start;
                $pauseEnd = now();
                $pauseDurationMinutes = $pauseStart->diffInMinutes($pauseEnd);

                // Update pause periods
                $pausePeriods = $activeShift->pause_periods ?? [];
                $pausePeriods[] = [
                    'reason' => $activeShift->current_pause_reason,
                    'start' => $pauseStart->toISOString(),
                    'end' => $pauseEnd->toISOString(),
                    'duration_minutes' => $pauseDurationMinutes
                ];

                $activeShift->pause_periods = $pausePeriods;
                $activeShift->total_pause_minutes += $pauseDurationMinutes;
            }

            // End the shift
            $activeShift->update([
                'logout_time' => now(),
                'shift_status' => 'ended',
                'current_pause_reason' => null,
                'current_pause_start' => null
            ]);

            $totalWorkHours = $activeShift->work_hours;

            return response()->json([
                'success' => true,
                'message' => 'Shift ended successfully!',
                'data' => [
                    'end_time' => now()->toISOString(),
                    'total_work_hours' => round($totalWorkHours, 2),
                    'total_pause_minutes' => $activeShift->total_pause_minutes
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to end shift. Please try again.'
            ], 500);
        }
    }

    /**
     * Get current shift status
     */
    public function getShiftStatus(Request $request)
    {
        try {
            $user = Auth::user();
            
            $activeShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->whereIn('shift_status', ['active', 'paused'])
                ->first();

            if (!$activeShift) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'has_active_shift' => false,
                        'status' => 'inactive'
                    ]
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'has_active_shift' => true,
                    'shift_id' => $activeShift->id,
                    'status' => $activeShift->shift_status,
                    'start_time' => $activeShift->login_time->toISOString(),
                    'current_session_minutes' => $activeShift->getCurrentSessionMinutes(),
                    'total_pause_minutes' => $activeShift->total_pause_minutes,
                    'current_pause_reason' => $activeShift->current_pause_reason,
                    'current_pause_start' => $activeShift->current_pause_start?->toISOString(),
                    'company_name' => $activeShift->company->name ?? 'Unknown'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get shift status.'
            ], 500);
        }
    }

    /**
     * Get today's work statistics
     */
    public function getTodayStats(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Today's completed shifts
            $todayShifts = StaffLogin::where('user_id', $user->id)
                ->whereDate('login_time', today())
                ->count();

            // Today's total work hours (excluding current active shift)
            $completedWorkHours = StaffLogin::where('user_id', $user->id)
                ->whereDate('login_time', today())
                ->whereNotNull('logout_time')
                ->get()
                ->sum('work_hours');

            // Add current active shift time if exists
            $activeShift = StaffLogin::where('user_id', $user->id)
                ->whereNull('logout_time')
                ->whereIn('shift_status', ['active', 'paused'])
                ->first();

            $currentShiftHours = 0;
            if ($activeShift) {
                $currentShiftHours = $activeShift->getCurrentSessionMinutes() / 60;
            }

            $totalWorkHours = $completedWorkHours + $currentShiftHours;

            return response()->json([
                'success' => true,
                'data' => [
                    'today_shifts' => $todayShifts,
                    'today_work_hours' => round($totalWorkHours, 2),
                    'current_shift_hours' => round($currentShiftHours, 2)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get today\'s statistics.'
            ], 500);
        }
    }
}
