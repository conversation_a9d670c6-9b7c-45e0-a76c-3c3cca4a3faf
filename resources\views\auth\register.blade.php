@extends('layouts.noauth')

@push('styles')
    <link href="{{ asset('src/assets/css/dark/authentication/auth-boxed.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('src/assets/css/light/authentication/auth-boxed.css') }}" rel="stylesheet" type="text/css" />

    {{-- Select2 CSS and Bootstrap 5 theme (optional) --}}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .repeat-bg-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 300px;
            transform: translate(-50%, -50%);
            background-image: url('/images/logo-dim.png');
            background-repeat: repeat;
            background-position: center;
            background-size: auto;
            z-index: 0;
        }

        .sign-in {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 32px;
        }

        .sign-in-btn {
            background-color: #E9A852;
            border-color: #E9A852;
            color: white;
            height: 56px;
        }

        .sign-in-btn:hover {
            background-color: #E9A852;
            border-color: #E9A852;
            color: white;
        }

        .card-shadow {
            box-shadow: 0px 24px 60px 0px #00000026;
        }

        #login-form input[type="email"],
        #login-form input[type="password"] {
            height: 56px !important;
        }

        .form-control.pr-5 {
            padding-right: 3rem !important;
        }

        /* Adjust icon positioning if needed */
        .position-absolute {
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
@endpush

@section('content')
    <div class="auth-container d-flex position-relative">
        <div class="repeat-bg-center"></div>
        <div class="container mx-auto align-self-center">
            <div class="row">
                <div class="col-xxl-4 col-xl-5 col-lg-5 col-md-8 col-12 d-flex flex-column align-self-center mx-auto">
                    <div class="card mt-3 mb-3 bg-white card-shadow rounded-xl border-0" style="border-radius: 16px">
                        <div class="card-body">
                            <form method="POST" action="{{ route('register.custom') }}">
                                @csrf
                                <div class="row">
                                <div class="col-md-12 mb-3 d-flex justify-content-center">
                                        <img src="{{ asset('images/logo.png') }}" class="img-fluid" alt="logo">
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <h2>Sign Up</h2>
                                        <p>Create your account to get started</p>
                                    </div>

                                    {{-- Name --}}
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">Name</label>
                                        <input type="text" name="name"
                                            class="form-control @error('name') is-invalid @enderror"
                                            value="{{ old('name') }}" required>
                                        @error('name')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    {{-- Email --}}
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" name="email"
                                            class="form-control @error('email') is-invalid @enderror"
                                            value="{{ old('email') }}" required>
                                        @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    {{-- Password --}}
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">Password</label>
                                        <input type="password" name="password"
                                            class="form-control @error('password') is-invalid @enderror" required>
                                        @error('password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    {{-- Confirm Password --}}
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">Confirm Password</label>
                                        <input type="password" name="password_confirmation" class="form-control" required>
                                    </div>

                                    {{-- Register Button --}}
                                    <div class="col-12">
                                        <div class="mb-4">
                                            <button class="btn sign-in-btn w-100">REGISTER</button>
                                        </div>
                                    </div>

                                    {{-- Already Registered --}}
                                    <div class="col-12 text-center">
                                        <p class="mb-0">Already have an account? <a href="{{ route('login.form') }}"
                                                class="text-warning">Login</a></p>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
