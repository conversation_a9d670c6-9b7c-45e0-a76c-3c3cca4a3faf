@extends('layouts.app')

@push('styles')
<style>
    .shift-form-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-header {
        background: linear-gradient(135deg, #E9A852 0%, #D4941F 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }

    .staff-selection {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
        border: 2px dashed #dee2e6;
    }

    .staff-checkbox {
        margin-bottom: 0.5rem;
    }

    .staff-checkbox input[type="checkbox"] {
        margin-right: 0.5rem;
    }

    .select-all-btn {
        background: #E9A852;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }
</style>
@endpush

@section('content')
<div class="shift-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <div class="form-header">
                        <h3><i class="fas fa-users-cog me-2"></i>Bulk Shift Assignment</h3>
                        <p class="mb-0">Assign shifts to multiple staff members at once</p>
                    </div>
                    
                    <div class="form-body p-4">
                        <form action="{{ route('shifts.bulk-store') }}" method="POST">
                            @csrf
                            
                            <!-- Staff Selection -->
                            <div class="staff-selection">
                                <div class="section-title mb-3">
                                    <i class="fas fa-users me-2"></i>Select Staff Members
                                </div>
                                
                                <button type="button" class="select-all-btn" onclick="toggleAllStaff()">
                                    <i class="fas fa-check-square me-1"></i>Select All
                                </button>
                                
                                <div class="row">
                                    @foreach ($users as $user)
                                        <div class="col-md-4">
                                            <div class="staff-checkbox">
                                                <label class="form-check-label">
                                                    <input type="checkbox" name="user_ids[]" value="{{ $user->id }}" class="staff-check">
                                                    {{ $user->name }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="company_id">
                                            <i class="fas fa-building me-1"></i>Company
                                        </label>
                                        <select name="company_id" id="company_id" class="form-control" required>
                                            <option value="">Select Company</option>
                                            @foreach ($companies as $company)
                                                <option value="{{ $company->id }}">{{ $company->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="shift_type">
                                            <i class="fas fa-tags me-1"></i>Shift Type
                                        </label>
                                        <select name="shift_type" id="shift_type" class="form-control" required>
                                            <option value="regular">Regular</option>
                                            <option value="overtime">Overtime</option>
                                            <option value="holiday">Holiday</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="start_time">
                                            <i class="fas fa-clock me-1"></i>Start Time
                                        </label>
                                        <input type="datetime-local" name="start_time" id="start_time" class="form-control" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="end_time">
                                            <i class="fas fa-clock me-1"></i>End Time
                                        </label>
                                        <input type="datetime-local" name="end_time" id="end_time" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="hourly_rate">
                                            <i class="fas fa-pound-sign me-1"></i>Hourly Rate (Optional)
                                        </label>
                                        <input type="number" name="hourly_rate" id="hourly_rate" class="form-control" step="0.01" min="0" placeholder="Leave empty for default rates">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" for="notes">
                                            <i class="fas fa-sticky-note me-1"></i>Notes (Optional)
                                        </label>
                                        <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Add notes for all shifts..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Assign Shifts to Selected Staff
                                </button>
                                <a href="{{ route('shifts.index') }}" class="btn btn-secondary btn-lg ms-2">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAllStaff() {
    const checkboxes = document.querySelectorAll('.staff-check');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    const btn = document.querySelector('.select-all-btn');
    btn.innerHTML = allChecked 
        ? '<i class="fas fa-check-square me-1"></i>Select All'
        : '<i class="fas fa-square me-1"></i>Deselect All';
}
</script>
@endsection
