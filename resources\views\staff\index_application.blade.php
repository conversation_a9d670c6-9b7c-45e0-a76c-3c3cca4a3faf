@extends('layouts.app')

@section('content')
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4>New Application</h4>
            <a href="{{ route('applications.create') }}" class="btn btn-warning">
                <i class="bi bi-plus"></i> Create Application
            </a>
        </div>

        <h5 class="mb-3">Application History</h5>

        <div class="row">
            @forelse($applications as $application)
                <div class="col-md-6 col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="fw-bold">{{ $application->title }}</h6>
                                    <small class="text-muted">{{ $application->application_date }}</small>
                                </div>
                                <span class="badge bg-light text-dark border rounded-pill px-2 py-1">
                                    {{ $application->applicationType?->name ?? 'N/A' }}
                                </span>
                            </div>

                            <p class="mb-2">
                                {{ Str::limit($application->description, 120) }}
                            </p>

                            <a href="javascript:void(0);" class="text-decoration-none text-primary" data-bs-toggle="modal"
                                data-bs-target="#viewApplicationModal" data-title="{{ $application->title }}"
                                data-type="{{ $application->applicationType?->name ?? 'N/A' }}"
                                data-date="{{ $application->application_date }}"
                                data-description="{{ $application->description }}">
                                View More <i class="bi bi-arrow-right-short"></i>
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <p class="text-center text-muted">No applications found.</p>
                </div>
            @endforelse
        </div>

        <div class="d-flex justify-content-center mt-4">
            {{ $applications->links() }}
        </div>
    </div>

    {{-- Modal --}}
    <div class="modal fade" id="viewApplicationModal" tabindex="-1" aria-labelledby="viewApplicationModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-white text-dark">
                <div class="modal-header border-bottom-0">
                    <div>
                        <h6 class="text-muted mb-1" id="modalApplicationType">Application Type</h6>
                        <h5 class="modal-title fw-bold" id="modalApplicationTitle">Application Title</h5>
                    </div>
                    <div class="d-flex align-items-start flex-column ms-auto">
                        <small class="text-muted" id="modalApplicationDate">2025-01-01</small>
                        <button type="button" class="btn-close mt-1" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <p id="modalApplicationDescription" class="mb-0" style="white-space: pre-line;"></p>
                </div>
                <div class="modal-footer border-top-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        const viewModal = document.getElementById('viewApplicationModal');
        viewModal.addEventListener('show.bs.modal', function(event) {
            const trigger = event.relatedTarget;

            const title = trigger.getAttribute('data-title');
            const type = trigger.getAttribute('data-type');
            const date = trigger.getAttribute('data-date');
            const description = trigger.getAttribute('data-description');

            document.getElementById('modalApplicationTitle').textContent = title;
            document.getElementById('modalApplicationType').textContent = type;
            document.getElementById('modalApplicationDate').textContent = date;
            document.getElementById('modalApplicationDescription').textContent = description;
        });
    </script>
@endpush
